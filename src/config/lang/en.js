export default {
  "": "",
  "null": "No more data",
  "A001": "Hello world",
  "Axiangdianliu": "A-phase i",
  "Axiangdianya": "A-phase voltage",
  "Axiangwugonggonglv": "A-phase q",
  "Axiangyougonggonglv": "A-phase p",
  "Axiangzongshizaigonglv": "A-total apparent power",
  "Bxiangdianliu": "B-phase i",
  "Bxiangdianya": "B-phase voltage",
  "Bxiangwugonggonglv": "B-phase q",
  "Bxiangyougonggonglv": "B-phase p",
  "Bxiangzongshizaigonglv": "B-total apparent power",
  "Captcha": "Captcha",
  "Captcha_tips01": "The length should be 4 characters",
  "Captcha_tips03": "The length should be 6 characters",
  "Captcha_tips02": "Retry after s% seconds",
  "ChangePassword": "Change password",
  "ChangePhone": "Modify phone number",
  "ChangeEmail": "Modify email address",
  "ConfirmPassword": "Confirm password",
  "ConfirmTheChanges": "Confirm",
  "Cxiangdianliu": "C-phase i",
  "Cxiangdianya": "C-phase voltage",
  "Cxiangwugonggonglv": "C-phase q",
  "Cxiangyougonggonglv": "C-phase p",
  "Cxiangzongshizaigonglv": "C-total apparent power",
  "DingTalk": "Dingtalk",
  "DingTalkMiniProgram": "Dingtalk mini program",
  "WeChatMiniProgram": "Wechat mini program",
  "Export_daochu": "Export",
  "Flat": "Flat",
  "GetVerificationCode": "Get verification code",
  "NewPassword": "New password",
  "NewPhone": "New phone",
  "NewEmail": "New email",
  "NextStep": "Next step",
  "OnPeak": "Peak",
  "OriginalPhone": "Original phone",
  "Password_tips01": "Please enter new password",
  "Password_tips02": "Please re-enter new password",
  "Password_tips03": "Please enter password",
  "Password_tips04": "At least 8 characters required",
  "Password_tips05": "Support input of numbers, english letters or english symbols",
  "Password_tips06": "Please re-enter your password",
  "Password_tips07": "The passwords you entered twice do not match",
  "Password_tips08": "Modification successful, please log in again",
  "Password_tips09": "Operation failed, try again later",
  "Password_tips10": "Password length must not be less than 6 characters",
  "Peak": "Top",
  "PleaseInputCaptcha": "Please input verification code",
  "PreviousStep": "Previous step",
  "Valley": "Valley",
  "WeChat": "Wechat",
  "alarm_chulirenyuan": "Handler",
  "alarm_chulishichang": "Processing time",
  "alarm_gaojingmingcheng": "Alarm name",
  "alarm_jiguibianhao": "Cabinet No",
  "alarm_options_0_4": "0-4 hours",
  "alarm_options_1_3": "1-3 days",
  "alarm_options_3more": "3 days+",
  "alarm_options_4_8": "4-8 hours",
  "alarm_options_8_24": "8-24 hours",
  "alarm_options_chulizhong": "Processing",
  "alarm_options_gaopinguzhang": "Frequent faults",
  "alarm_options_gaoyouguzhang": "High-priority faults",
  "alarm_options_quanbushichang": "All durations",
  "alarm_options_quanbuzhuangtai": "All",
  "alarm_options_shoudonggongdan": "Manual work order",
  "alarm_options_shoudongguzhang": "Manual faults",
  "Resolved": "Resolved",
  "alarm_quanbuleixing": "All types",
  "alarm_quanburenyuan": "All personnel",
  "alarm_yichangleixing": "Anomaly type",
  "alarm_yichangliebiao": "Anomaly list",
  "alarm_yichangmingcheng": "Anomaly name",
  "alarm_yichangtongji": "Anomaly statistics",
  "alarm_yichangzhuangtai": "Anomaly status",
  "alarm_zanwuyichang": "No abnormalities",
  "alarm_zhandianmingcheng": "Station name",
  "biangengriqi": "Changing the commissioning date, please proceed with caution!",
  "bianjizhandianxinxi": "Edit station info",
  "bingwang": "On-grid",
  "buchongshuoming": "Additional explanation",
  "buchongxinxi": "Additional info",
  "Successed": "Success",
  "Failed, please try again later": "Failed, please try again later",
  "Vehicles": "Vehicles",
  "car_cheliangzongshu": "Total vehicles",
  "car_chezhanzongshu": "Total stations",
  "car_dianxingeshu": "Number of cells",
  "Cell type": "Cell type",
  "car_edingrongliang": "Rated capacity",
  "car_leijixunhuancishu": "Cycle count",
  "car_lixiancheliang": "Offline",
  "car_tips01": "No real-time cell data available",
  "car_yueleijihuoyueshu": "Monthly active count",
  "car_zaixiancheliang": "Online",
  "car_zongjihuoyueshu": "Total active count",
  "car_zuorihuoyuecheliang": "Yesterday's active vehicles",
  "changePwd01": "Password change recommended",
  "changePwd02": "We recommend changing your initial password immediately after your first login.",
  "changeUsername": "Modify username",
  "chezhandizhi": "Station address",
  "chezhanleixing": "Station type",
  "chezhanmingcheng": "Station name",
  "chezhanzuobiao": "Station coordinates",
  "chongdianchengben": "Charging cost",
  "Charge/Discharge Ranking": "Charge/Discharge ranking",
  "Running Time Ranking": "Running time ranking",
  "chuangdu_tips": "The length is between s% and e% characters",
  "chuangjianshijian": "Create time",
  "All Abnormal": "All anomaly",
  "ciyao": "Minor",
  "code_10011": "Organization not activated",
  "code_10012": "Phone number authorization required",
  "code_10013": "The account is not registered. please verify and re-enter.",
  "code_10040": "Sms verification code validation failed",
  "code_10041": "Sms sending failed",
  'code_error': 'Error, please try again later.',
  "Save": "Save",
  "common_bianji": "Edit",
  "common_fou": "No",
  "common_ge": '',
  "common_guanbi": "Close",
  "common_hour": "Hour",
  "common_jieshiriqi": "End date",
  "common_jieshuriqi": "End date",
  "common_jieshushijian": "End time",
  "Last 30 Days": "Last 30 days",
  "common_jintian": "Today",
  "Last Year": "Last year",
  "Last Week": "Last week",
  "common_kaishiriqi": "Start date",
  "common_kaishishijian": "Start time",
  "common_nian": "Year",
  "Cancle": "Cancle",
  "common_ri": "Day",
  "common_rihuizong": "Daily summary",
  "common_riqi": "Date",
  "common_shi": "Yes",
  "common_tian": "Day",
  "common_tiao": "",
  "common_tiaozhi": "Jump to",
  "common_wanyuan": '',
  "common_xiaoshihuizong": "Hourly summary",
  "common_ye": "Page",
  "common_yuan": '',
  "common_yue": "Month",
  "common_yuehuizong": "Monthly summary",
  "common_zuo": '',
  "daichuli": "Pending",
  "device_chanpinxinghao": "Product model",
  "device_chushi": "Dehumidify",
  "device_dianxinfenxi": "Cell analysis",
  "device_dianxinxinghao": "Cell model",
  "device_fangchafenxi": "Cell variance analysis",
  "device_jichafenxi": "Cell range analysis",
  "ins": "Insulation resistance",
  "device_kechongdianliang": "Rechargeable capacity",
  "device_kefangdianliang": "Dischargeable capacity",
  "device_leijichongdianliang": "Total charging",
  "device_leijifangdianliang": "Total discharging",
  "device_pingjundianya": "Avg voltage",
  "device_pingjunwendu": "Avg temp",
  "Device No": "Device No",
  "device_shebeimingcheng": "Device name",
  "device_shebeixinghao": "Device name",
  "device_shebeixuhao": "Device serial number",
  "device_suoshubianyaqi": "Associated transformer",
  "device_type_chunengdianbiao": "Energy meter",
  "device_type_chushiji": "Dehumidifier",
  "device_type_cu": "Rack",
  "device_type_dianwangdianbiao": "Grid meter",
  "Cell": "Cell",
  "device_type_dui": "Cluster",
  "device_type_xiaofang": "Fire protection",
  "device_type_yelengji": "Liquid cooler",
  "device_zongdianliu": "Total current",
  "device_zongdianya": "Total voltage",
  "device_zuididianya": "Min voltage",
  "device_zuidiwendu": "Min temp",
  "device_zuigaodianya": "Max voltage",
  "device_zuigaowendu": "Max temp",
  "dianchibao": "Battery pack s%",
  "dianliubianbi": "Current ratio",
  "dianyabianbi": "Voltage ratio",
  "ditu": "Map",
  "enterprise_dangqiansezhi": "Current color value",
  "enterprise_dengluyebeijing": "Login page background",
  "enterprise_qiyeguanli": "Enterprise",
  "enterprise_qiyelogo": "Enterprise logo",
  "enterprise_qiyemingcheng": "Enterprise name",
  "enterprise_qiyexinxi": "Enterprise info",
  "enterprise_qiyeyanse": "Enterprise color",
  "enterprise_qiyezhuti": "Enterprise theme",
  "enterprise_tips001": "The administrator is the only user with the highest privileges on the application side, having all operational and data permissions.",
  "enterprise_tips002": "The current color value can be switched for system theme color configuration.",
  "enterprise_tips003": "If you need to customize the system's primary domain, please refer to this document for instructions.",
  "enterprise_tips004": "Enterprise name cannot be empty",
  "enterprise_wangyebiaoti": "Web page title",
  "enterprise_wangyeico": "Web page icon",
  "enterprise_wangyeicon": "Web page icon",
  "enterprise_wangyeyuming": "Web page domain",
  "enterprise_zhuguanliyuan": "Administrator",
  "export_tips01": "Would you like to export data from s% to s%?",
  "export_tips02": "Would you like to export data for s%?",
  "fangdianshouyi": "Discharging revenue",
  "fanxiangyougongzongdianneng": "Reverse active energy",
  "fashengyuanyin": "Cause",
  "gaojing": "Alarm",
  "gaojingbianhao": "Alarm No",
  "gaojingjibie": "Alarm level",
  "gaojingzhuangtai": "Alarm status",
  "genjinren": "Follow-up person",
  "gongdan": "Work order",
  "gongdanbianhao": "Work order No",
  "gongdanleixing": "Work order type",
  "gongdanliebiao": "Work order list",
  "gongdanmingcheng": "Work order name",
  "gongdantongji": "Work order statistics",
  "gongdanxiangqing": "Work order detail",
  "gongdanzhuangtai": "Work order status",
  "gonglvyinshu": "Power factor",
  "guanliangaojingxinxi": "Related alarm info",
  "guanliyuan": "Administrator",
  "haobianyaqi": "Transformer n%",
  "haobianyaqirongliang": "Transformer n% capacity",
  "home_dianliangzhibiao": "Energy metrics",
  "home_fugaichengshi": "Covered cities",
  "home_shouyizhibiao": "Revenue metrics",
  "home_zhandianliebiao": "Station list",
  "home_zhandianzongshu": "Station count",
  "huluegaojing": "Ignore alarm",
  "jiazaigengduo": "Load more",
  "jibie": "Level",
  "jiejue": "Resolve",
  "jieshuyuefen": "End mth",
  "jiesuo": "Unlock",
  "jinji": "Urgent",
  "kaishiyuefen": "Start mth",
  "latitude": "Latitude",
  "leixing": "Type",
  "liebiao": "List",
  "lijichakan": "View",
  "lijiqianwang": "Go now",
  "liwang": "Off-grid",
  "login_anquantuichu": "Log out",
  "login_daojishi": "Countdown",
  "login_huoquyanzhengma": "Send code",
  "login_login": "Login",
  "login_mimadenglu": "Password login",
  "login_tips01": "I understand and agree to the user agreement to assist or login",
  "login_tips02": "Please enter the sms verification code",
  "login_weidenglu": "Not logged in",
  "login_yanshizhanghaodenglu": "Demo account login",
  "Verification code login": "Phone login",
  "longitude": "Longitude",
  "menu_anquantuichu": "Log out",
  "Alert Notification": "Alert notification",
  "Settings": "Settings",
  "User Guide": "User guide",
  "Add Station": "Add station",
  "AddStation": "Add station",
  "Account": "Account",
  "Management": "Management",
  "Dashboard": "Dashboard",
  "message_429": "Frequent requests, please try again later",
  "message_wrongSMSCode": "Sms verification code validation failed",
  "Reset Success!": "Reset success!",
  "No Station Bound Yet": "No station bound yet",
  "URL address not configured yet": "Url address not configured yet",
  "The maximum date range cannot exceed 7 days": "The maximum date range cannot exceed 7 days",
  "Name": "Name",
  "tips_bind_r": "To view device operation status and data online.",
  "tips_bind_l": "After binding your account, you can access the",
  "DingTalkScan": "Bind via dingtalk QR code",
  "DingTalkBind": "Dingtalk account binding",
  "Basic Info": "Basic info",
  "Password": "Password",
  "WeChatScan": "Bind via wechat QR code",
  "WeChatBind": "Wechat account binding",
  "tips_bind_DingTalk": "Dingtalk account bound: s%, scan the QR code to access.",
  "tips_bind_WeChat": "Wechat account bound: s%, scan the QR code to access.",
  "Account Binding": "Account binding",
  "No more data": "No more data",
  "opeartion_caozuoneirong": '',
  "opeartion_caozuoren": '',
  "opeartion_caozuorizhi": '',
  "Time of occurrence": 'Time',
  "opeartion_options_dianjiaguanli": '',
  "opeartion_options_donghuanguanli": '',
  "opeartion_options_nengxiaoguanli": '',
  "opeartion_options_qiyeguanli": '',
  "opeartion_options_shujudaochu": '',
  "opeartion_options_xunidianchang": '',
  "opeartion_options_yonghuguanli": '',
  "opeartion_shijianleixing": '',
  "Bind": "Bind",
  "Edit User": "Edit user",
  "Edit Sub-account": "Edit sub-account",
  "View Cell Analysis": "View cell analysis",
  "Return to Overall Device": "Return to overall device",
  "Copy": "Copy",
  "Unbind": "Unbind",
  "Delete": "Delete",
  "tips_unbind_DingTalk": "Are you sure you want to unbind this account?",
  "tips_unbind_WeChat": "Are you sure you want to unbind this account?",
  "Refresh": "Refresh",
  "add_device": "Add device",
  "Add User": "Add user",
  "Add Sub-account": "Add sub-account",
  "Edit": "Edit",
  "Rank": "Rank",
  "Revenue rank": "Revenue rank",
  "Charge/Discharge rank": "Charge/Discharge rank",
  "phone": "Phone",
  "tell": "Phone",
  "Mail": "Mail",
  "Phone/Mail": "Phone / mail",
  "Please enter a valid phone number": "Please enter a valid phone number",
  "Please enter a valid phone number or email": "Please enter a valid phone number or email",
  "Please enter at least one phone number or email": "Please enter at least one phone number or email",
  "phone_tips02": "New number cannot be the same as old number",
  "email_tips02": "New email cannot be the same as old email",
  "placeholder": "Input content search",
  "placeholder_phone": "Please enter the phone number",
  "placeholder_qingshuru": "Please enter",
  "placeholder_qingshuruguanliyuanshoujihaoma": "Please enter the administrator's phone number",
  "placeholder_qingshuruguanliyuanxingmiing": "Please enter the administrator's name",
  "placeholder_qingshuruguanliyuanxingming": "Please enter the administrator's name",
  "placeholder_qingshurumima": "Password",
  "placeholder_qingshuruxingmiing": "Please enter the name",
  "placeholder_qingshuruxingming": "Please enter the name",
  "placeholder_qingshuruzhandianbianhao": "Please enter the station number",
  "placeholder_qingshuruzhandianmingcheng": "Please enter the station name",
  "placeholder_qingshuruzizhanghumingcheng": "Please enter the sub-account name",
  "placeholder_qingxuanze": "Please select",
  "placeholder_qingxuanzebangdingqiye": "Please select an enterprise to bind",
  "placeholder_qingxuanzebianyaqishuliang": "Please select the number of transformers",
  "placeholder_qingxuanzequanxian": "Please select permissions",
  "placeholder_qingxuanzeriqi": "Please select the date",
  "placeholder_qingxuanzesuoshubianyaqi": "Please select the associated transformer",
  "placeholder_qingxuanzeweihurenyuan": "Please select maintenance personnel",
  "placeholder_qingxuanzeyewufanwei": "Please select the business scope",
  "placeholder_qingxuanzezhandiandiqu": "Please select the region",
  "placeholder_qingxuanzezhandiandizhi": "Please select the address",
  "placeholder_sousuodidian": "Search location",
  "qingshangchuantupian": "Place upload picture",
  "qingxuanzechezhanleixing": "Please select station type",
  "qingxuanzegenjinren": "Please select follow-up person",
  "qita": "Other",
  "All": "All",
  "All Level": "All level",
  "Confirm": "Confirm",
  "Date Select": "Date select",
  "Upload Failed": "Upload failed",
  "Upload Image": "Upload image",
  "Device Management": "Device",
  "Service Life Distribution": "Service life distribution",
  "Operating Hours Distribution": "Operating hours distribution",
  "Device Status": "Device status",
  "Duration(h)": "(hour)",
  "IsResolved?": "Resolved",
  "Are you sure?": "Are you sure?",
  "Are you sure unlock?": "Are you sure unlock the vehicle?",
  "Are you sure lock?": "Are you sure lock the vehicle?",
  "locked": 'locked',
  "Are you sure to delete": 'Are you sure to delete?',
  "Time": "Time",
  "Service life(month)": "(month)",
  "h": "H",
  "The phone number has already been registered": "The phone number has already been registered",
  "Home": "Home",
  "Quantity(Liang)": "Quantity",
  "soc": "SOC",
  "SOC": "Soc",
  "SOC Distribution": "Soc distribution",
  "soh": "SOH",
  "SOH": "SOH",
  "staion_igbtwendu": "Igbt temp",
  "station_24xiaoshidianliangbianhua": "24 hours soc",
  "station_bangdingqiye": "Bind enterprise",
  "No": "No",
  "station_bianliuqi": "PCS",
  "station_bianyaqishuliang": "Number of transformers",
  "station_bingwangzhuangtai": "Grid connection",
  "station_bmsfuwei": "BMS reset",
  "station_celueguanli": "Strategy manage",
  "station_chakanzhandianxiangqing": "View station details",
  "station_chongfangdianliang": "Charge/Discharge amount",
  "station_chongfangdianqushi": "Charge/Discharge trend",
  "station_chongfangdianshouyi": "Charge/Discharge revenue",
  "station_chongfangdiantongji": "Charge/Discharge statistics",
  "station_chongfangdianzhuangtai": "Charging status",
  "station_chuneng": "EMS",
  "station_chunengdianbiaoyougonggonglv": "EMS meter active power",
  "station_chunengzhandianSOC": "Station soc",
  "station_chushiji": "Dehumidifier",
  "station_chushuiwendu": "Outlet temp",
  "station_conongdu": "Co concentration",
  "station_cuneidainxinwenchafenxi": "Cell temperature difference analysis",
  "station_cuneidianxinyachafenxi": "Cell voltage difference analysis",
  "station_dakai": "Open",
  "station_dianbiao": "Meter",
  "station_dianchiguanlixitong": "BMS",
  "station_dianwang": "Grid",
  "station_dianwangdianbiaoyougonggonglv": "Grid meter active power",
  "station_ditu": "Map",
  "station_donghuanguanlixitong": "Environmental system",
  "station_fangdiandachenglv": "Discharge completion rate",
  "station_fenggutaolishouyi": "Revenue",
  "station_fenzhonghuizong": "Minute summary",
  "station_fuwushang": "Provider",
  "station_fuzai": "Load",
  "station_gonglv": "Power",
  "station_gonglüqushifenxi": "Power trend analysis",
  "station_gongshuiyali": "Outlet pressure",
  "station_guanlihoutai": "O&m management",
  "station_huanjingshidu": "Ambient humidity",
  "station_huanjingwendu": "Ambient temp",
  "station_huishuiwendu": "Inlet temp",
  "station_huishuiyali": "Inlet pressure",
  "station_jiaoqianyiri": "Vs. prev. day",
  "station_jigui": "Container",
  "station_jiting": "Emergency stop",
  "station_jitingkaiguan": "Emergency stop switch",
  "station_kehu": "Customer",
  "station_leijichongdianliang": "Charging energy",
  "station_leijifangdianliang": "Discharging energy",
  "station_menjin": "Access control",
  "station_menjinkaiguan": "Access control switch",
  "station_options_ceshijieduan": "Testing stage",
  "station_options_dagongyeyongdian": "Large industrial electricity",
  "station_options_yibangongshangyeyongdian": "General commercial electricity",
  "station_options_zhengshitouyun": "Official commissioning",
  "station_pcsfuwei": "PCS reset",
  "Previous day: No data": "Previous day: no data",
  "Device Type": "Device type",
  "Device info": "Device info",
  "station_shengyudianliang": "Capacity",
  "station_shouyi": "Revenue",
  "station_shouyiqushi": "Revenue trend",
  "station_shouyitongji": "Revenue statistics",
  "station_shuijin": "Water immersion",
  "station_shuijinkaiguan": "Water immersion switch",
  "station_tianjiazhandian": "Bind enterprise",
  "station_touyunriqi": "Deploy date",
  "station_touyunzhuangtai": "Commissioning status",
  "station_vocnongdu": "Voc concentration",
  "station_weihurenyuan": "Maintenance personnel",
  "Temp": "Temp",
  "station_xiaofangyangan": "Fire protection",
  "station_xitongxiaolv": "System efficiency",
  "station_yanwunongdu1": "Smoke concentration 1",
  "station_yanwunongdu2": "Smoke concentration 2",
  "station_yelengjifuwei": "Liquid cooler",
  "station_yongdianleixing": "Electricity type",
  "station_yueleiji": "Monthly",
  "station_yueleijishouyi": "Monthly revenue",
  "station_yunxingxiaolv": "Efficiency",
  "station_yunxingzhuangtai": "Operating status",
  "station_zhandianbianhao": "Station No",
  "station_zhandiandiqu": "Region",
  "station_zhandiandizhi": "Station address",
  "Station Name": "Station name",
  "station_zhandiantupian": "Station image",
  "station_zhandianzuobiao": "Coordinates",
  "station_zhanshiziduan": "Display fields",
  "station_zhengtiyunxingzhuangtai": "Overall status",
  "station_zhuangjigonglv": "Rated power",
  "station_zhuangjirongliang": "Rated capacity",
  "station_zonghexiaolv": "System efficiency",
  "station_zongji": "Total",
  "station_zongshouyi": "Total revenue",
  "station_zuorichongdianliang": "Yesterday charge",
  "station_zuorifangdianliang": "Yesterday discharge",
  "station_zuorishouyi": "Yesterday's revenue",
  "status_chongdian": "Charging",
  "status_chongdianzhong": "Charging",
  "status_cuowu": "Wrong",
  "status_daiji": "Standby",
  "status_fangdian": "Discharging",
  "status_fangdianzhong": "Discharging",
  "status_lixian": "Offline",
  "Activated": 'Activated',
  "NotActivated": "Not activated",
  "status_yichang": "Anomaly",
  "status_zaixian": "Online",
  "status_zhengchang": "Normal",
  "sub_guanliyuanshouji": "Administrator phone",
  "sub_guanliyuanxingming": "Administrator name",
  "sub_kaitongzizhanghuquanxian": "Sub-account create permission",
  "sub_options_donglidianchi": "Power battery",
  "sub_options_gongshangyechuneng": "Industrial energy storage",
  "sub_sfjyktzzhqx": "Enable sub-account access",
  "sub_tianjiashijian": "Add time",
  "sub_tips01": "Check this box to grant the account permission to create sub-accounts (no need to check when creating accounts for end users)",
  "sub_yewufanwei": "Business scope",
  "sub_zichanghuguanli": "Sub-account",
  "sub_zichanghumingcheng": "Sub-account name",
  "suoche": "Lock",
  "system_chuneng": "Energy storage",
  "system_dongli": "Power battery",
  "textarea_tips01": "Please provide additional explanation for the cause",
  "tianjia": "Add",
  "tianjiashebei_tips01": "Device cannot be empty",
  "tianjiashebei_tips02": "Device No cannot be empty",
  "tianjiashebei_tips03": "Device No must be unique",
  "tianjiashebei_tips04": "Transformer cannot be empty",
  "tianjiashebei_tips05": "Device name cannot be empty",
  "tianjiashebei_tips06": "Vehicle type cannot be empty",
  "tianjiashebei_tips07": "Cell model cannot be empty",
  "tianjiashebei_tips08": "Device name must be unique",
  "tianjiashebei_tips09": "At least one device must be retained",
  "tianjiashebei_tips10": 'Battery No cannot be empty',
  "tianjiashebei_tips11": 'Battery No must be unique',
  "tishi": "Tips",
  "to": "To",
  "tongji": "Statistics",
  "tupian": "Image",
  "tupian_tips": "Image desc",
  "unit_danwei": "Unit",
  "unit_shi": "Hour",
  "upload_chang": "Length",
  "upload_chicunyaoqiu": "Size requirements",
  "upload_kuan": "Width",
  "upload_shangchuanwenjian": "Upload file",
  "upload_tips001": "Size must not exceed",
  "upload_tips002": "Image format error, the image format should be",
  "upload_tips003": "Image format error, the image format should be s% or s%",
  "user_EMSkongzhiquanxian": "EMS Access Control",
  "user_DataRange": "Data range",
  "placeholder_qingxuanzeshujufanwei": "Please select data range",
  'kejianzhandian': 'Visible sites',
  "qingxuanzekejianzhandian": 'Please select visible sites',
  "shangjiqiye": "Parent company",
  "user_quanxian": "Permissions",
  "user_shangcidenglushujian": "Last login time",
  "user_xingming": "Name",
  "user_yonghuguanli": "User",
  "username": "Username",
  "weixiujilu": "Maintenance record",
  "weixiujilu_tips01": "Please provide additional explanation for the maintenance record",
  "wuyichang": "No abnormalities",
  "xiangqing": "Detail",
  "xinjian": "Add",
  "xinzenggongdan": "Add work order",
  "yibaozhang": "Reported",
  "yichangbianhao": "Anomaly No",
  "yichangjibie": "Anomaly level",
  "yichangshijian": "Anomaly time",
  "yichangxiangqing": "Anomaly detail",
  "yichu": "Remove",
  "yihuifu": "Recovered",
  "yihulue": "Ignored",
  "zanwushuju": "No data",
  "zhengxiangyougongzongdianneng": "Forward active energy",
  "zhongyao": "Major",
  "zhuangtai": "Status",
  "zhuangtaifenbu": "Status distribution",
  "zhuanweigongdan": "Convert to work order",
  "zongshizaigonglv": "Apparent power",
  "zongwugonggonglv": "Reactive power",
  "zongyougonggonglv": "Active power",
  "notAccepted": 'You have not accepted the agreement',
  'login': 'Login',
  'demoLogin': 'Demo account login',
  "UnderstandAndAgree": 'Understand and agree',
  "UserAgreement": 'User agreement',
  "toLogin": 'to register or log in',
  "SMSCode": 'SMS verification code',
  "allData": "All data",
  "OnlySelectedData": 'Pre-selected data',
  "OrganizationName": 'Organization name',
  'operation': 'Operation',
  'zhandianshitu': 'Site view',
  'shebeishitu': 'Device view',
  'Device Filter': 'Filter',
  'BatteryNo': 'Battery No',
  'BMS型号': 'BMS model',
  'Capacity': 'Capacity',
  'service_expire_time': 'Expiration date',
  //
  "Onlined": "Online",
  "Chinese": 'Chinese',
  "Metrics View": "Metrics view",
  "Relay Status": "Relay status",
  "signal_strength": "Signal strength",
  'signal4g': 'Signal',
  "Location": "Location",
  "Cell Series Count": "Cell series count",
  "Cell temperature sensor count": "Cell temperature sensor count",
  "Positive insulation resistance": "Positive insulation resistance",
  "System time": "System time",
  "Negative insulation resistance": "Negative insulation resistance",
  'Recovery time': 'Recovery time',
  "Project name": "Project name",
  "Power type": 'Power type',
  "runTime": 'Run time',
  "Yesterday charge": "Yesterday's charge",
  "Yesterday discharge": "Yesterday's discharge",
  "当前异常": "Current exception",
  "今日新增": 'Today added',
  "七日新增": 'Seven days added',
  "实时状态": "Real-time info",
  "历史数据": "History data",
  "是否确认启用": "Are you sure to enable",
  "是否确认停用": "Are you sure to discontinue",
  "Deactivate": "Deactivate",
  "Device status statistics": "Device status statistics",
  "Charge and discharge capacity": "Charge/Discharge capacity",
  "Average running time": "Average running time",
  "Customer Name": "Customer name",
  "Expired": "Expired",
  "Custom Columns": "Custom columns",
  "batch_add": "Batch add",
  "column_settings": "Column settings",
  "Select all": "Select all",
  "invert_selection": "Invert selection",
  "batch_add_device": "Batch add device",
  "step_one": "Step one",
  "step_one_desc": "Complete the table content according to the prompts",
  "download_template": "Download template",
  "step_two": "Step two: upload the completed template",
  "drag_upload_tip": "Drag the modified template file here or click to upload",
  "filter": "Filter",
  "filter_by_project": "By project",
  "filter_by_customer": "By customer",
  "filter_by_group": "By group",
  "activation_status": "Activation status",
  "system_status": "System status",
  "service_status": "Service status",
  "activation_date": "Activation date",
  "last_data_time": "Last data time",
  "alarm_count": "Alarm count",
  "total_charge_time": "Total charge time(h)",
  "total_charge_capacity": "Total charge capacity(ah)",
  "total_discharge_time": "Total discharge time(h)",
  "total_discharge_capacity": "Total discharge capacity(ah)",
  "last_charge_time": "Last charge time",
  "last_discharge_time": "Last discharge time",
  "cycle_count": "Cycle count",
  "reset": "Reset",
  "charging time": 'Charging time',
  "CellPackaging": 'Cell packaging',
  "Discharging Time": "Discharging time",
  "BatteryNumber": 'Battery tanks',
  "AccumulatedChargingCapacity": "Charging capacity",
  "Individual voltage": "Individual voltage",
  "AccumulatedDischargingCapacity": "Discharging capacity",
  "RatedTotalVoltage": "Rated voltage",
  "software_version": "Software version",
  "manufacture_date": "Manufacture date",
  "rated_total_current": "Rated total current",
  "rated_energy": "Rated energy",
  "operation_data": "Operation data",
  "device_data": "Device data",
  "statistical_analysis": "Statistical analysis",
  "charging_behavior_analysis": "Charging behavior analysis",
  "charging_period_distribution": "Charging period distribution",
  "SOC_distribution_at_the_start_of_charging": "Soc distribution at the start of charging",
  "single_charge_capacity_distribution": "Single charge capacity distribution",
  "InputToSearch01": 'Search by device No or name',
  'sys_voltage': 'System voltage',
  'sys_currents': 'System currents',
  'ins_p': 'Positive insulation',
  'ins_n': 'Negative insulation',
  'vol_max': 'Max cell voltage',
  'vol_min': 'Min cell voltage',
  'vol_dif': 'Voltage differential',
  'temp_max': 'Max cell temp',
  'temp_min': 'Min cell temp',
  'chger_out_volt': 'Charger output voltage',
  'chger_out_curr': 'Charger output currents',
  'cc_res': 'CC resistance value',
  'cc2_res': 'CC2 resistance value',
  'cp_freq': 'CP frequency',
  'cp_pwm': 'CP duty cycle',
  'Total Discharge Cap': 'Total discharge cap',
  'Total Charge Cap': 'Total charge cap',
  "voltage_details": 'Voltage details',
  "temp_details": 'Temp details',
  "Historical range": "Historical range",
  "Historical variance": "Historical variance",
  "charging time(h)": "Charging time (h)",
  'time (h)': "Time(h)",
  "Charging times": "Charging times",
  'Battery capacity': 'Capacity(%)',
  "Quantity": "Quantity",
  "Charge/discharge capacity": "Capacity",
  "Initial SOC": "Initial soc",
  "Final SOC": "Final soc",
  "This time duration": "This time duration",
  "duration": "Duration",
  "Collection time": "Collection time",
  "Temperature sensing number": "Temperature sensing number",
  "System Capacity": "System capacity",
  "System SOC": "System soc",
  "System SOH": "System soh",
  "Charging and discharging status": "Status",
  "Max voltage position": "Max voltage position",
  "Min voltage position": "Min voltage position",
  "Average voltage": "Average voltage",
  "Max temperature position": "Max temperature position",
  "Min temperature position": "Min temperature position",
  "Temp difference": "Temp difference",
  "Alarm status": "Alarm status",
  'Number of alarms': "Number of alarms",
  "Heartbeat status": 'Heartbeat status',
  "High voltage detection": "High voltage detection",
  "CC2 resistance": "Cc2 resistance",
  "Charge request current": "Charge request current",
  "Charge Request voltage": "Charge request voltage",
  "Input signal status": "Input signal status",
  "4G signal": "4g signal",
  "Reserved longitude": "Reserved longitude",
  "Reserved latitude": "Reserved latitude",
  "Lock status": "Lock status",
  "Bms model": "BMS model",
  "CC resistance": "Cc resistance",
  "CP duty cycle": "Cp duty cycle",
  "CP frequency": "Cp frequency",
  "Charging communication status": "Charging communication status",
  "Charger fault status": "Charger fault status",
  "Total Energy": "Total energy",
  "MOS temperature": "Mos temperature",
  "Cycle times": "Cycle times",
  "Balanced mode": "Balanced mode",
  "Balance current": "Balance current",
  "Balanced logo": "Balanced logo ",
  "Fault Code": "Fault code",
  "Voltage": "Voltage",
  "Device alarm": "Device alarm",
  "Power battery management": "Power battery management",
  "Project management": "Project",
  "Alarm Management": "Alarm",
  "Add project": "Add project",
  "Search or create a new option": "Search or create a new option",
  "Create new options": "Create new options",
  "Battery series count": "Battery series count",
  "Battery parallel count": "Battery parallel count",
  "Battery box count": "Battery box count",
  "Rated power": "Rated power",
  "Rated current": "Rated current",
  "Rated capacity": "Rated capacity",
  "Rated energy": "Rated energy",
  "Initiation date": "Initiation date",
  "Project description": "Description",
  "Project No": "Project No",
  "Main customer": "Main customer",
  "Total devices": "Total devices",
  "Online": "Online",
  "Activated1": "Activated",
  "Activated devices": "Activated devices",
  "Online devices": "Online devices",
  "Device model": "Device model",
  "Vehicle type": "Vehicle type",
  "Region": "Region",
  "Total capacity": "Total capacity",
  "Total power": "Total power",
  "Charging duration": "Charge duration",
  "Charging amount": "Charge capacity",
  "Charge": "Charge",
  "Discharging duration": "Discharge duration",
  "Discharging amount": "Discharge capacity",
  "Discharge": "Discharge",
  "Alarm count": "Alarm count",
  "Project detail": "Project detail",
  "Belonging project": "Belonging project",
  "Activation time": "Active date",
  "Only Excel files can be uploaded !": "Only excel files can be uploaded !",
  "Please upload the file first": "Please upload the file first",
  "SMS verification": "Sms verification",
  "Email Verification": "Email verification",
  "Current Email": "Current email",
  "Please enter a valid email": "Please enter a valid email",
  "Verification code sent successfully": "Verification code sent successfully",
  "Failed to send verification code": "Failed to send verification code",
  "Verification failed": "Verification failed",
  "Failed to update email": "Failed to update email",
  "Initial Password": "Initial password",
  "Show Password": "Show password",
  "Hide Password": "Hide password",
  "Password copied to clipboard": "Password copied to clipboard",
  "Password must be 6-20 characters long and contain only letters, numbers, and special characters (!@#$%)": "Password must be 6-20 characters long and contain only letters, numbers, and special characters (!@#$%)",
  "Email": "Email",
  "Administrator Email": "Administrator email",
  "Project information configuration": "Project information configuration",
  // "":"",
  "Date Of Production": "Date of production",
  "Download QR Code": "Download QR code",
  "Download": "Download",
  "Look QR Code": "QR code",
  "Vehicle Info": "Vehicle info",
  "Vehicle No": "Vehicle No",
  "Operation": "Operation",
  "Choose Device": "Choose device",
  "Selected": "Selected",
  "Belonged City": "City",
  "Bind Devices": "Bind",
  "Delete Device": "Delete",
  "Device QR Code": "Device QR code",
  "Bind Single Device": "Bind single device",
  "Device Name": "Device name",
  "Power Battery": "Power battery",
  "Energy Storage Battery": "Energy storage battery",
  "Device Naming": "Device naming",
  "Please Input Vehicle Name": "Please input vehicle name",
  "Maximum length: 10": "Maximum length: 10",
  "Are you sure to delete：": "Are you sure to delete：",
  "Do you want to batch download the QR codes of the devices on the current page?": "Do you want to batch download the QR codes of the devices on the current page?",
  "TotalEnergy": "Total energy",
  "Cell Details": "Cell details",
  "Voltage Thermal Distribution": "Voltage thermal distribution",
  "Temperature Thermal Distribution": "Temperature thermal distribution",
  "Min": "Min",
  "Max": "Max",
  'Cell No.': "Cell No.",
  'Sensor No.': 'Sensor No.',
  "The device has been bound to the customer!": "The device has been bound to the customer!",
  "The device is bound to the project!": "The device is bound to the project!",
  "电池健康度": "Soh",
  "Average Value": "Average",
  "Next day": "Next day",
  "Clear": "Clear",
  "Alarm Info": "Alarm info",
  "History Alarm": "History alarm",
  "Loading": 'Loading',
  "There is no abnormality": "There is no abnormality",
  "No historical anomalies": "No historical anomalies",
  "Operation Status": "Status",
  "Signal": "Signal",
  "No abnormal historical data": "No anomaly historical data",
  "History Info": "History info",
  "Temp Sensor": "Temp sensor",
  "Operation record": "Operation record",
  "tai": '',
  "Loading Energy": "Total energy",
  "Yesterday's active devices": "Yesterday's active devices",
  "envTemp": "Environment temperature",
  "heatTemp": "Heating temperature",
  "heatCur": "Heating current",
  "faultFlag": "Fault flag",
  "vccVolt": "Vcc voltage",
  "keyOnVolt": "Key on voltage",
  'hv': 'High voltage detection',
  "Deleting Records": "Deleting records",
  "View Configuration": "View configuration",
  "Battery Health Management Platform": "Battery Health Management Platform",
  "Empower the full lifecycle health management of batteries with AI to ensure operational safety and extend system lifespan ": "Empower the full lifecycle health management of batteries with AI to ensure operational safety and extend system lifespan ",
  "上善能及能源云": "EVFREY",
  "Devices add": "Add",
  "Batch Operations": "Batch operations",
  "Service Renewal": "Service renewal",
  "Device capacity": 'Total capacity',
  "Station": "Station",
  "Social Contribution": "Social contribution",
  "Standard coal saved (tons)": "Standard coal saved (tons)",
  "CO₂ emission reduction (tons)": "CO₂ emission reduction (tons)",
  "Equivalent tree planting amount (pieces)": "Equivalent tree planting amount",
  "Map Perspective": "Map perspective",
  "Site Dashboard": "Site dashboard",
  "The overall calculation logic of such data indicators is based on basic data such as system profit and electricity savings, combined with parameters such as carbon emission factors, to calculate the quantitative contribution of the energy storage system in energy conservation and emission reduction.": "The overall calculation logic of such data indicators is based on basic data such as system profit and electricity savings, combined with parameters such as carbon emission factors, to calculate the quantitative contribution of the energy storage system in energy conservation and emission reduction.",
  "Are you sure you want to reset?": "Are you sure you want to reset?",
  "Export data": "Export data",
  "Service Date":  "Service date",
  "Please select service date":"Please select service date",
  "Standard User":"Standard user",
  "Sub Admin":"Sub admin",
}