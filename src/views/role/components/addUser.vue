<template>
    <div class="add-user">
        <a-form
            ref="formRef"
            :model="formState"
            :rules="rules"
            class="add-form"
            autocomplete="off"
            labelAlign="left"
            :label-col="labelCol"
            hideRequiredMark
        >
            <a-form-item :label="$t('user_xingming')" name="realName">
                <a-input
                    v-model:value="formState.realName"
                    :placeholder="$t('placeholder_qingshuruxingming')"
                    :maxLength="12"
                />
            </a-form-item>
            <a-form-item :label="$t('phone')" name="phone">
                <a-input
                    v-model:value="formState.phone"
                    :placeholder="$t('placeholder_phone')"
                    :maxLength="11"
                >
                    <template #prefix>
                        <span
                            class="text-third-text dark:text-60-dark inline-block px-1.5 rounded-full bg-f5f7f7 dark:bg-transparent"
                            >+86</span
                        >
                    </template>
                </a-input>
            </a-form-item>
            <a-form-item :label="$t('Email')" name="email">
                <a-input
                    v-model:value="formState.email"
                    :placeholder="$t('Email')"
                    type="email"
                />
            </a-form-item>
            <a-form-item
                :label="$t('Initial Password')"
                name="password"
                v-if="!props.data"
            >
                <a-input
                    v-model:value="formState.password"
                    :placeholder="$t('Initial Password')"
                    :type="passwordVisible ? 'text' : 'password'"
                    :maxLength="20"
                >
                    <template #suffix>
                        <div class="password-actions select-none">
                            <div
                                @click="togglePasswordVisibility"
                                :title="
                                    passwordVisible
                                        ? $t('Hide Password')
                                        : $t('Show Password')
                                "
                                class="cursor-pointer text-base"
                            >
                                <EyeOutlined v-if="passwordVisible" />
                                <EyeInvisibleOutlined v-else />
                            </div>
                            <span
                                @click="copyPassword"
                                :title="$t('Copy')"
                                class="cursor-pointer copy-icon"
                            >
                                <iconSvg name="copy" class="w-4 h-4" />
                            </span>
                        </div>
                    </template>
                </a-input>
            </a-form-item>
            <a-form-item :label="$t('User Type')" name="roleId">
                <a-select
                    v-model:value="formState.roleId"
                    :options="userRoles"
                    :placeholder="$t('User Type')"
                >
                </a-select>
            </a-form-item>
            <a-form-item
                :label="$t('user_quanxian')"
                name="permissions"
                v-if="isBooan"
            >
                <a-select
                    v-model:value="formState.permissions"
                    mode="multiple"
                    :options="[
                        {
                            label: $t('user_EMSkongzhiquanxian'),
                            value: 'emsControl',
                        },
                    ]"
                    :placeholder="$t('placeholder_qingxuanzequanxian')"
                >
                </a-select>
            </a-form-item>
            <a-form-item :label="$t('user_DataRange')" name="resourceScope">
                <a-select
                    v-model:value="formState.resourceScope"
                    :options="dataRanges"
                    :placeholder="$t('placeholder_qingxuanzeshujufanwei')"
                    @change="changeDataRange"
                />
            </a-form-item>
            <a-form-item
                :label="$t('kejianzhandian')"
                name="viewableStationIds"
            >
                <a-tree-select
                    v-model:value="formState.viewableStationIds"
                    style="width: 100%"
                    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                    :tree-data="sites"
                    :placeholder="$t('qingxuanzekejianzhandian')"
                    tree-default-expand-all
                    multiple
                    :replaceFields="{
                        children: 'children',
                        title: 'name',
                        key: 'id',
                        value: 'id',
                    }"
                    :disabled="NodesDisabled"
                >
                </a-tree-select>
                <!-- <el-cascader :options="sites" :props="props2" clearable /> -->
            </a-form-item>
        </a-form>
    </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { message } from 'ant-design-vue'
import {
    EyeOutlined,
    EyeInvisibleOutlined,
    CopyOutlined,
} from '@ant-design/icons-vue'
import { email, newPassword } from '@/common/reg'

const { t, locale } = useI18n()
const labelCol = computed(() => {
    let res =
        locale.value == 'zh'
            ? {
                  span: 4,
              }
            : locale.value == 'en'
            ? {
                  span: 7,
              }
            : {
                  span: 7,
              }
    return res
})
const store = useStore()
const isBooan = computed(() => {
    return store.state.user.userInfoData.roles.includes('org_owner')
})

const props = defineProps({
    data: { type: Object },
    sites: { type: Array, default: () => [] },
    userRoles: { type: Array, default: () => [] },
})
const formState = reactive({
    staffId: void 0,
    realName: void 0,
    phone: void 0,
    email: void 0,
    password: void 0,
    roleId: void 0,
    permissions: [],
    resourceScope: void 0,
    viewableStationIds: [],
})

// 密码显示状态
const passwordVisible = ref(false)

const formRef = ref(null)

// 手机号或邮箱至少有一个的验证器
const phoneValidator = async (rule, value) => {
    const phone = formState.phone
    const emailValue = formState.email

    // 检查手机号格式
    if (phone) {
        const phoneReg = /^1[3456789]\d{9}$/
        if (!phoneReg.test(phone)) {
            return Promise.reject(t('Please enter a valid phone number'))
        }
    }
    // 检查是否至少有一个
    if (!phone && !emailValue) {
        return Promise.reject(
            t('Please enter at least one phone number or email')
        )
    }

    return Promise.resolve()
}
// 手机号或邮箱至少有一个的验证器
const emailValidator = async (rule, value) => {
    const phone = formState.phone
    const emailValue = formState.email

    // 检查邮箱格式
    if (emailValue) {
        if (!email.test(emailValue)) {
            return Promise.reject(t('Please enter a valid email'))
        }
    }

    // 检查是否至少有一个
    if (!phone && !emailValue) {
        return Promise.reject(
            t('Please enter at least one phone number or email')
        )
    }
    return Promise.resolve()
}

// 密码验证器
const passwordValidator = async (_rule, value) => {
    if (!value) {
        return Promise.reject(t('placeholder_qingshuru'))
    } else {
        if (newPassword.test(value)) {
            return Promise.resolve()
        } else {
            return Promise.reject(
                t(
                    'Password must be 6-20 characters long and contain only letters, numbers, and special characters (!@#$%)'
                )
            )
        }
    }
}

const rules = {
    realName: [
        {
            required: true,
            message: t('placeholder_qingshuruxingming'),
            trigger: 'blur',
        },
    ],
    phone: [
        {
            required: false,
            trigger: ['change', 'blur'],
            validator: phoneValidator,
        },
    ],
    email: [
        {
            required: false,
            trigger: ['change', 'blur'],
            validator: emailValidator,
        },
    ],
    password: [
        {
            required: true,
            trigger: ['change', 'blur'],
            validator: passwordValidator,
        },
    ],
    roleId: [
        {
            required: false,
            // message: t('Please select user type'),
            // trigger: 'change',
        },
    ],
    position: [{ required: false }],
    staffId: [{ required: false }],
}

const submitRules = async () => {
    if (!formState.resourceScope) {
        message.error(t('请选择数据范围'))
        return false
    }
    if (
        formState.resourceScope == 'specify' &&
        (!formState.viewableStationIds ||
            formState.viewableStationIds?.length <= 0)
    ) {
        message.error(t('请选择可见站点'))
        return false
    }

    // 检查手机号或邮箱至少有一个
    const phone = formState.phone
    const emailValue = formState.email

    if (!phone && !emailValue) {
        message.error(t('Please enter at least one phone number or email'))
        return false
    }

    // 表单验证
    try {
        await formRef.value.validate()
    } catch (error) {
        return false
    }

    return true
}

// 切换密码显示状态
const togglePasswordVisibility = () => {
    passwordVisible.value = !passwordVisible.value
}

// 复制密码
const copyPassword = async () => {
    if (formState.password) {
        try {
            await navigator.clipboard.writeText(formState.password)
            message.success(t('Password copied to clipboard'))
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea')
            textArea.value = formState.password
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
            message.success(t('Password copied to clipboard'))
        }
    }
}

const clearValidate = () => {
    passwordVisible.value = false
    Object.keys(formState).forEach((key) => {
        formState[key] = undefined
    })
    formRef.value.clearValidate()
}

const resetFields = () => {
    Object.keys(formState).forEach((key) => {
        formState[key] = undefined
    })
    passwordVisible.value = false
    formRef.value.resetFields()
}

defineExpose({ submitRules, clearValidate, resetFields, formState })

watch(
    () => props.data,
    (val) => {
        if (val) {
            Object.keys(val).forEach((key) => {
                formState[key] = val[key]
                if (key == 'permissions') {
                    formState[key] = val[key] ? val[key] : []
                }
            })

            // 处理roleCodes转换为roleId用于回显
            if (val.roleCodes && Array.isArray(val.roleCodes)) {
                formState.roleId = getRoleIdByRoleCodes(val.roleCodes)
            }
        }
    },
    { immediate: true }
)
const dataRanges = ref([
    {
        label: t('allData'),
        value: 'all',
    },
    {
        label: t('OnlySelectedData'),
        value: 'specify',
    },
])
const NodesDisabled = computed(() => {
    return formState.resourceScope == 'all'
})

const changeDataRange = (e) => {
    console.log(e)
    if (e == 'all') {
        formState.viewableStationIds = []
    } else {
        //
    }
}

// 根据roleCodes数组获取对应的roleId（用于编辑时回显）
const getRoleIdByRoleCodes = (roleCodes) => {
    if (!roleCodes || !Array.isArray(roleCodes)) {
        return undefined
    }

    // 优先级：1(主管理员) > 2(子管理员) > 其他
    if (roleCodes.includes(1)) {
        return 1
    } else if (roleCodes.includes(2)) {
        return 2
    }

    return undefined
}
</script>

<style lang="less" scoped>
.add-user {
    :deep(.ant-form) {
        .ant-form-item-has-success {
            margin-bottom: 24px !important;
        }
        .ant-form-item-with-help {
            margin-bottom: 0px !important;
        }
        .ant-form-item {
            font-size: 14px;
            margin-bottom: 24px;
            .ant-form-item-label {
                width: 90px;
                font-size: 14px;
                color: rgba(34, 34, 34, 0.65);
                > label {
                    height: 32px;
                    font-size: 14px;
                }
            }

            .ant-form-item-explain,
            .ant-form-item-explain-error {
                min-height: 24px;
                font-size: 14px;
            }

            .ant-form-item-control-input {
                min-height: 32px;
                .ant-form-item-control-input-content {
                    input {
                        padding: 4px 11px;
                        font-size: 14px;
                    }

                    .ant-input {
                        &:hover {
                            border-color: var(--themeColor);
                        }

                        &:focus {
                            border-color: var(--themeColor);
                            box-shadow: none;
                        }
                    }

                    .ant-select-multiple {
                        font-size: 14px;
                        .ant-select-selector {
                            padding: 1px 4px;
                            font-size: 14px;
                            .ant-select-selection-item {
                                height: 24px;
                                margin-top: 2px;
                                margin-bottom: 2px;
                                line-height: 22px;
                                margin-inline-end: 4px;
                                padding-inline-start: 8px;
                                padding-inline-end: 8px;
                            }
                            &::after {
                                line-height: 24px;
                            }
                        }

                        .ant-select-selection-search-input,
                        .ant-select-multiple
                            .ant-select-selection-search-mirror {
                            height: 24px;
                        }
                    }

                    .ant-select {
                        &:not(.ant-select-disabled) {
                            &:hover {
                                .ant-select-selector {
                                    border-color: var(--themeColor);
                                }
                            }
                        }
                    }
                }
            }
        }

        .ant-select-focused {
            .ant-select-selector {
                border-color: var(--themeColor);
                box-shadow: none !important;
            }
        }
    }
}
.ant-select-dropdown {
    z-index: 9999 !important;
}
:deep(.ant-select-clear) {
    background-color: transparent;
    right: 30px;
}

.password-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    line-height: 1;
    color: var(--input-color);
    .copy-icon {
        line-height: 1;
        margin-right: 8px;
        display: flex;
        align-items: center;
    }
}

:deep(.ant-input-affix-wrapper .ant-input-prefix) {
    margin-right: 0;
}
:deep(.ant-input-affix-wrapper) {
    padding: 0;
}
</style>
