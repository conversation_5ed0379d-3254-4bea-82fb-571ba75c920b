<template>
    <div class="enterprise-management">
        <mw-table
            :dataSource="dataSource"
            :columns="columns"
            :hasPage="true"
            :pageConfig="{ changePage, paginationProps }"
            :customRow="Rowclick"
            :loading="loading"
            :rowKey="(record) => record.staffId"
            @change="onTableChange"
            :showRefresh="false"
            class="alarm-table"
        >
            <template #realName="{ text, record }">
                <div class="bold-box">
                    {{ text }}
                    <span v-if="record.owner" class="user-type-tag">
                        {{ $t('enterprise_zhuguanliyuan') }}
                    </span>
                    <span
                        v-else-if="getUserTypeLabel(record.roleId)"
                        class="user-type-tag"
                    >
                        {{ getUserTypeLabel(record.roleId) }}
                    </span>
                </div>
            </template>

            <template #resourceScope="{ record }">
                {{
                    record.resourceScope == 'all'
                        ? $t('allData')
                        : $t('OnlySelectedData')
                }}
            </template>
            <template #permissions="{ record }">
                <CheckOutlined
                    v-if="
                        record.permissions &&
                        record.permissions.includes('emsControl')
                    "
                />
            </template>
        </mw-table>

        <el-drawer
            v-model="visible"
            :size="486"
            :show-close="false"
            @close="onClose"
            wrapClassName="drawerBox"
        >
            <template #header>
                <div
                    class="drawer-header flex items-center justify-between leading-5.5"
                >
                    <div
                        class="drawer-header text-primary-text dark:text-80-dark"
                    >
                        <span>{{
                            edit ? $t('Edit User') : $t('Add User')
                        }}</span>
                    </div>
                    <div class="flex gap-x-3 items-center">
                        <el-button plain round @click="onClose">{{
                            $t('Cancle')
                        }}</el-button>

                        <confirm-button
                            v-if="
                                edit ? (clickData?.owner ? false : true) : false
                            "
                            :title="$t('Are you sure to delete')"
                            @confirm="onConfirm"
                        >
                            <template #reference>
                                <el-button plain round>{{
                                    $t('Delete')
                                }}</el-button>
                            </template>
                        </confirm-button>
                        <el-button
                            plain
                            type="primary"
                            round
                            :loading="addLoading"
                            @click="submit"
                            >{{ $t('Save') }}</el-button
                        >
                    </div>
                </div>
            </template>
            <addUser
                ref="addUserForm"
                :data="clickData"
                :sites="AllSites"
                :userRoles="userRoles"
            />
        </el-drawer>
    </div>
</template>

<script setup>
import { ref, onMounted, createVNode, computed } from 'vue'
import { usePagenation } from '@/common/setup'
import apiService from '@/apiService/device'
import addUser from './addUser.vue'
// import enterpriseDetailTop from './enterpriseDetailTop.vue'
import { message, Modal } from 'ant-design-vue'
import { CheckOutlined } from '@ant-design/icons-vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
const { t, locale } = useI18n()
const store = useStore()
const dataSource = ref([])
const loading = ref(false)
const visible = ref(false)
const addUserForm = ref(null)
const edit = ref(false)
const addLoading = ref(false)
const clickData = ref(void 0)

const Rowclick = (record) => {
    return {
        onClick: () => {
            clickData.value = record
            edit.value = true
            visible.value = true
        },
    }
}
const getStaffPageData = async () => {
    const params = {
        current: pageParam.value.current,
        size: pageParam.value.size,
    }
    try {
        loading.value = true
        const {
            data: {
                data: { records, total },
            },
        } = await apiService.getStaffPage(params)
        loading.value = false
        dataSource.value = records || []
        paginationProps.value.total = total
    } catch (error) {
        loading.value = false
    }
}

const { paginationProps, changePage, onTableChange, refresh, pageParam } =
    usePagenation(getStaffPageData)

const columns = [
    {
        title: t('user_xingming'),
        dataIndex: 'realName',
        key: 'realName',
        slots: {
            customRender: 'realName',
        },
        width: '20%',
    },
    {
        title: t('phone'),
        dataIndex: 'phone',
        key: 'phone',
        slots: {
            customRender: 'phone',
        },
        width: '15%',
        align: 'center',
    },
    {
        title: t('Email'),
        dataIndex: 'email',
        key: 'email',
        slots: {
            customRender: 'email',
        },
        width: '20%',
        align: 'center',
    },
    {
        title: t('user_EMSkongzhiquanxian'),
        dataIndex: 'permissions',
        key: 'permissions',
        slots: {
            customRender: 'permissions',
        },
        width: '10%',
        align: 'center',
    },
    {
        title: t('user_DataRange'),
        dataIndex: 'resourceScope',
        key: 'resourceScope',
        slots: {
            customRender: 'resourceScope',
        },
        width: '10%',
        align: 'center',
    },
    {
        title: t('user_shangcidenglushujian'),
        dataIndex: 'lastLoginTime',
        key: 'lastLoginTime',
        slots: {
            customRender: 'lastLoginTime',
        },
        width: '15%',
        align: 'right',
    },
]

const addUserFun = (boolean) => {
    visible.value = true
    edit.value = boolean
    clickData.value = void 0
}

const onClose = () => {
    addUserForm.value.resetFields()
    addUserForm.value.clearValidate()
    addLoading.value = false
    visible.value = false
    clickData.value = void 0
}

const addStaff = async (params) => {
    console.log('params---', params)
    if (params.resourceScope == 'all') {
        params.viewableStationIds = undefined
    }
    try {
        addLoading.value = true
        const {
            data: { code },
        } = await apiService.addStaff(params)
        if (code === 0) {
            message.success(t('Successed'))
            onClose()
            refresh()
        }
        // if (code === 10015) {
        //     message.error(t('The phone number has already been registered'))
        // }
        addLoading.value = false
    } catch (error) {
        addLoading.value = false
    }
}

const editModifyStaff = async (params) => {
    try {
        addLoading.value = true
        const {
            data: { code },
        } = await apiService.editModifyStaff(params)
        if (code === 0) {
            message.success(t('Successed'))
            onClose()
            refresh()
        }
        addLoading.value = false
    } catch (error) {
        addLoading.value = false
    }
}

const submit = async () => {
    const isValid = await addUserForm.value.submitRules()
    console.log('validation result---', isValid)
    if (isValid) {
        // 获取表单数据
        const formData = addUserForm.value.formState
        if (!edit.value) {
            addStaff(formData)
        } else {
            editModifyStaff({ ...formData, staffId: clickData.value.staffId })
        }
    }
}

const btnLoading = ref(false)

const onConfirm = async () => {
    btnLoading.value = true
    const {
        data: { code },
    } = await apiService.removeStaff({
        staffId: clickData.value.staffId,
    })
    if (code === 0) {
        message.success(t('Successed'))
        btnLoading.value = false
        onClose()
        refresh()
    }
}

defineExpose({ addUserFun })

const AllSites = ref([])
const getCompanyInfo = computed(() => store.getters['user/getUserInfoData'])

const orgId = ref(
    getCompanyInfo?.value?.orgId ? getCompanyInfo.value.orgId : void 0
)

// 数据处理方法
const processTreeData = (nodes, depth = 0) => {
    return nodes.map((node) => ({
        ...node,
        disabled: node.resourceType != 'station',
        children: node.children ? processTreeData(node.children) : [],
    }))
}
const getAllSites = async () => {
    //
    // const res = await apiService.getSubSupplierWithOwnerTree()
    const res = await apiService.getOrgWithStationTopology({
        orgId: orgId.value,
    })
    console.log(res)
    AllSites.value = processTreeData([res.data.data])
}
const userRoles = ref([
    // {
    //     value: 1,
    //     label: t('Administrator'),
    // },
    {
        value: 2,
        label: t('Standard User'),
    },
    {
        value: 5,
        label: t('Sub Admin'),
    },
])

// 根据roleId获取用户类型标签
const getUserTypeLabel = (roleId) => {
    const role = userRoles.value.find((role) => role.value === roleId)
    return role ? role.label : ''
}
onMounted(() => {
    getStaffPageData()
    getAllSites()
})
</script>

<style scoped lang="less">
.enterprise-management {
    // margin-top: 16px;
    .table-title {
        font-size: 16px;
        color: #222222;
        margin-top: 28px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .bt {
            display: flex;
            align-items: center;
        }

        :deep(.addUser) {
            width: 20px;
            height: 20px;
        }
    }

    .alarm-table {
        :deep(.ant-table-row) {
            cursor: pointer;
        }

        :deep(.ant-table-thead > tr > th) {
            background: none;
            font-weight: none;
            color: var(--text-60) !important;
        }

        :deep(.ant-table-tbody > tr > td) {
            padding: 16px;
            font-size: 14px;
            color: var(--text-80);
        }
    }

    .bt {
        padding: 4px 15px;
        font-size: 14px;
        height: 32px;
    }

    .bold-box {
        // font-size: 16px !important;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .user-type-tag {
        padding: 0 8px;
        background: rgba(22, 119, 255, 0.1);
        border-radius: 2px;
        height: 22px;
        color: #1677ff;
        font-size: 12px;
        line-height: 22px;
        display: inline-flex;
        align-items: center;
    }
}
:deep(.el-dialog) {
    background: var(--input-bg);
    backdrop-filter: blur(10px);
    .el-dialog__title {
        color: var(--text-100);
    }
    .el-dialog__headerbtn .el-dialog__close {
        color: var(--text-60);
    }
    .el-dialog__body {
        color: var(--text-80);
    }
}
</style>

<style lang="less">
.ant-modal-confirm-btns {
    float: none;
    text-align: center;
}

.my-modal {
    width: 350px !important;

    .ant-modal-confirm-title {
        font-size: 16px;

        + .ant-modal-confirm-content {
            margin-left: 38px;
        }
    }

    .ant-modal-confirm-content {
        margin-top: 8px;
        font-size: 14px;
    }

    .ant-modal-body {
        padding: 32px 32px 24px;
    }

    .ant-modal-confirm-btns {
        float: none;
        margin-top: 24px;

        .ant-btn {
            height: 32px;
            line-height: 16px;
            font-size: 14px;
            padding: 6px 12px;
            box-sizing: border-box;
            background: #f5f7f7;
            color: var(--text-100);
            border-color: #f5f7f7;

            &:hover {
                border-color: #f5f7f7;
            }
        }

        .ant-btn-primary {
            color: #fff;
            border-color: var(--themeColor);
            background-color: var(--themeColor);

            &:hover {
                color: #fff;
                border-color: var(--themeColor);
            }
        }
    }
}
</style>
