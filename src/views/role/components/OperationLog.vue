<template>
    <div class="relative z-20">
        <mw-table
            :dataSource="dataSource"
            :columns="columns"
            :hasPage="true"
            :loading="loading"
            :rowKey="(record) => record.id"
            :pageConfig="{ changePage, paginationProps }"
            @change="onTableChange"
            :showRefresh="false"
            class="alarm-table"
        >
            <template #type="{ record }">
                {{ getOperationName(record.operationLogModule) }} -
                {{ record.operationLogTypeName }}
            </template>
            <template #operator="{ record }">
                {{ record.staffName }}
            </template>
            <template #createTime="{ record }">
                {{ record.createTime }}
            </template>
        </mw-table>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { usePagenation } from '@/common/setup'
import apiVpp from '@/apiService/vpp'
import api from '@/apiService'

// 定义props
const props = defineProps({
    operationLogModule: {
        type: String,
        default: '',
    },
    dateSelect: {
        type: Object,
        default: () => ({
            periodType: 'day',
            startDate: '',
            endDate: '',
        }),
    },
})

// 定义emit
const emit = defineEmits(['update:dataSource'])
const columns = [
    {
        title: '操作内容',
        dataIndex: 'content',
        key: 'content',
        slots: {
            customRender: 'content',
        },
        width: '40%',
    },
    {
        title: '事件类型',
        dataIndex: 'type',
        key: 'type',
        slots: {
            customRender: 'type',
        },

        align: 'left',
    },
    {
        title: '操作人',
        dataIndex: 'operator',
        key: 'operator',
        slots: {
            customRender: 'operator',
        },

        align: 'center',
    },
    {
        title: '发生时间',
        dataIndex: 'createTime',
        key: 'createTime',
        slots: {
            customRender: 'createTime',
        },

        align: 'right',
    },
]
const dataSource = ref([])
const loading = ref(false)
const getData = async () => {
    const params = {
        current: pageParam.value.current,
        size: pageParam.value.size,
    }
    const res = await apiVpp.getOperationLogPage({
        ...params,
        ...props.dateSelect,
        operationLogModule: props.operationLogModule,
    })
    paginationProps.value.total = res.data.data.total
    paginationProps.value.current = res.data.data.current
    dataSource.value = res.data.data.records
    // 向父组件发送数据更新
    emit('update:dataSource', dataSource.value)
}
const { paginationProps, changePage, onTableChange, refresh, pageParam } =
    usePagenation(getData)

// 监听props变化，重新获取数据
watch(
    () => [props.operationLogModule, props.dateSelect],
    () => {
        paginationProps.value.current = 1
        getData()
    },
    { deep: true }
)

const options = ref([])
const getTypeList = async () => {
    const res = await api.getDictByType({
        type: 'operationLogModule',
    })
    options.value = res.data.data
}
const getOperationName = (val) => {
    return options.value.find((item) => item.value == val)?.label
}
onMounted(async () => {
    await getTypeList()
    await getData()
})
</script>

<style lang="less" scoped>
// .ant-table-tbody > tr > td {
//     padding: 16px;
//     font-size: 14px;
//     color: var(--text-80);
// }
:deep(.mw-table .ant-table-tbody > tr > td) {
    padding: 16px;
}
</style>
