<template>
    <div class="alarm-page">
        <!-- 使用 alarmOverview 组件 -->
        <AlarmOverview
            :getAlarmDataFlag="true"
            stationType="energy_storage_cabinet"
            :showSnFilter="true"
        />
    </div>
</template>

<script>
import AlarmOverview from '../components/alarmOverview.vue'

export default {
    name: 'AlarmPage',
    components: {
        AlarmOverview,
    },
}
</script>

<style scoped lang="less">
.alarm-page {
    height: 100%;
    background: var(--bg-color);
    border-radius: 8px;
    padding: 20px;
}
</style>
